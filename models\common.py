import torch
from torch import nn
import torch.nn.functional as F

__all__ = [
    "FeedForwardNet",  # 前馈网络
    "FeatAttention",  # 特征注意力
    "WeightedAggregator",  # 加权聚合器
]


class FeedForwardNet(nn.Module):
    """前馈网络类，
    由*n_layers*个线性层组成，使用PReLU和Dropout（最后一层除外）
    采用Xavier初始化并使用ReLU增益
    """

    def __init__(self, in_feats: int, hidden: int, out_feats: int, n_layers: int, dropout: float):  # 初始化方法
        super().__init__()  # 调用父类初始化方法
        self.layers = nn.ModuleList()  # 创建层列表
        self.n_layers = n_layers  # 设置层数
        if n_layers == 1:  # 如果只有一层
            self.layers.append(nn.Linear(in_feats, out_feats))  # 添加从输入特征到输出特征的线性层
        else:  # 如果有多层
            self.layers.append(nn.Linear(in_feats, hidden))  # 添加从输入特征到隐藏特征的线性层
            for _ in range(n_layers - 2):  # 添加中间层
                self.layers.append(nn.Linear(hidden, hidden))  # 添加从隐藏特征到隐藏特征的线性层
            self.layers.append(nn.Linear(hidden, out_feats))  # 添加从隐藏特征到输出特征的线性层
        if n_layers > 1:  # 如果有多层
            self.prelu = nn.PReLU()  # 创建参数化ReLU激活函数
            self.dropout = nn.Dropout(dropout)  # 创建Dropout层
        self._reset_parameters()  # 重置参数

    # 工具函数
    def _reset_parameters(self):  # 参数重置方法
        gain = nn.init.calculate_gain("relu")  # 计算ReLU激活函数的增益值
        for layer in self.layers:  # 遍历所有层
            nn.init.xavier_uniform_(layer.weight, gain=gain)  # 使用Xavier均匀分布初始化权重
            nn.init.zeros_(layer.bias)  # 将偏置初始化为零

    # 前向传播
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 计算前馈网络输出
        for i, layer in enumerate(self.layers):  # 遍历所有层
            x = layer(x)  # 应用线性层
            if i < self.n_layers - 1:  # 如果不是最后一层
                x = self.dropout(self.prelu(x))  # 应用PReLU激活函数和Dropout
        return x  # 返回输出


class FeatAttention(nn.Module):
    """
    特征注意力类,
    用于计算多个特征之间的注意力分数并加权求和
    """

    def __init__(self, feat_dim: int, num_heads: int, attn_drop: float = 0.0, negative_slope: float = 0.2):  # 初始化方法
        super().__init__()
        self.hop_attn_l = nn.Parameter(torch.FloatTensor(1, num_heads, feat_dim))  # 创建左侧跳级注意力参数
        self.hop_attn_r = nn.Parameter(torch.FloatTensor(1, num_heads, feat_dim))  # 创建右侧跳级注意力参数
        self.attn_dropout = nn.Dropout(attn_drop)  # 创建注意力dropout层
        self.leaky_relu = nn.LeakyReLU(negative_slope)  # 创建LeakyReLU激活函数
        self._reset_parameters()  # 重置参数

    def _reset_parameters(self):  # 参数重置方法
        gain = nn.init.calculate_gain("relu")  # 计算ReLU激活函数的增益值
        nn.init.xavier_normal_(self.hop_attn_l, gain=gain)  # 使用Xavier正态分布初始化左侧注意力参数
        nn.init.xavier_normal_(self.hop_attn_r, gain=gain)  # 使用Xavier正态分布初始化右侧注意力参数

    def forward(self, feats):  # 前向传播方法
        astack_l = [(feat * self.hop_attn_l).sum(dim=-1).unsqueeze(-1) for feat in feats]  # 计算每个特征与左侧注意力参数的乘积并求和
        a_r = (feats[0] * self.hop_attn_r).sum(dim=-1).unsqueeze(-1)  # 计算第一个特征与右侧注意力参数的乘积并求和
        astack = torch.cat([(a_l + a_r).unsqueeze(-1) for a_l in astack_l], dim=-1)  # 将所有注意力分数连接起来
        a = self.leaky_relu(astack)  # 应用LeakyReLU激活函数
        a = F.softmax(a, dim=-1)  # 应用softmax归一化
        a = self.attn_dropout(a)  # 应用注意力dropout
        out = 0  # 初始化输出
        for i in range(a.shape[-1]):  # 遍历所有特征
            out += feats[i] * a[..., i]  # 加权求和
        return out  # 返回输出


class WeightedAggregator(nn.Module):
    """加权聚合器"""

    def __init__(self, num_feats: int, in_feats: int, num_hops: int):
        super().__init__() 
        self.agg_feats = nn.ParameterList()  # 创建聚合特征参数列表
        for _ in range(num_hops):  # 为每一跳创建参数
            p = nn.Parameter(torch.Tensor(num_feats, in_feats))  # 创建参数
            nn.init.xavier_uniform_(p)  # 使用Xavier均匀分布初始化参数
            self.agg_feats.append(p)  # 添加参数到列表

    def forward(self, feats):  # 前向传播方法
        new_feats = []  # 初始化新特征列表
        for feat, weight in zip(feats, self.agg_feats):  # 遍历特征和权重
            new_feats.append((feat * weight.unsqueeze(0)).sum(dim=1).squeeze())  # 计算加权特征并添加到新特征列表
        return new_feats  # 返回新特征列表 