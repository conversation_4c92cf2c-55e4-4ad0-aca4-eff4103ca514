## SubInfer: 大规模异构图表示学习与推理框架

### 项目简介
SubInfer 基于 PyTorch、DGL 与 PyG，面向大规模异构图的链接预测与节点分类。核心思路是“元路径驱动的子图划分 + 子图级传播”，结合分布式训练与缓存，加速在 DBLP、PubMed、Yelp、Freebase、OGB-MAG 等数据上的建模与推理。

### 主要特性
- 多模型：NARS、SeHGNN、RGCN、RGAT、RGSN、RHGNN
- 任务：链接预测、节点分类
- 元路径划分：按元路径构造语义图，Metis 划分子图并补全“影响节点”
- 训练策略：先对子图一次性传播得到全图表示，再训练轻量级预测头（LP）；或在子图上端到端训练（NC）
- 分布式与缓存：原生支持 torchrun DDP；子图磁盘缓存、传播阶段内存/显存统计

---

## 目录结构（精简）
```
SubInfer/
├─ models/             # 模型实现（nars、sehgnn、rgat、rgcn、rgsn、rhgnn、common）
├─ preprocess/         # 数据预处理与PKL文件生成（data_preprocess.py, complex_emb.py）
├─ scripts/            # 任务脚本（link_*.py，classify_*.py）
├─ dataset/            # 数据集（DBLP/PubMed/Yelp/Freebase/Ogbn_mag）
├─ partition/          # 子图划分文件（*.pkl）
├─ utils.py            # 子图划分、数据加载、评估等通用工具
├─ requirements.txt    # 依赖
└─ README.md

```

---

## 环境与安装
- Python 3.8–3.10（推荐 3.10）
- 建议使用 CUDA 11.3 对应预编译包（requirements.txt 已固定版本）

安装依赖：
```bash
pip install -r requirements.txt
```
如本机 CUDA 版本不同，请根据注释中的源（torch/dgl/pyg wheels）替换为合适版本。

---

## 数据准备
将原始 .dat 文件放入对应目录：
- dataset/DBLP: node.dat, link.dat, label.dat, label.dat.test
- dataset/PubMed: node.dat, link.dat, label.dat, label.dat.test
- dataset/Yelp: node.dat, link.dat, link.dat.test（meta.dat 可选）
- dataset/Freebase: node.dat, link.dat, label.dat, label.dat.test（meta.dat 可选）

生成训练所需的 PKL 文件（按需运行）：
```bash
# DBLP（节点分类）
python - <<'PY'
from preprocess.data_preprocess import load_DBLP
load_DBLP()
PY
# DBLP（链接预测）
python - <<'PY'
from preprocess.data_preprocess import load_DBLP_link
load_DBLP_link()
PY
# PubMed（节点分类）
python - <<'PY'
from preprocess.data_preprocess import load_PubMed
load_PubMed()
PY
# PubMed（链接预测）
python - <<'PY'
from preprocess.data_preprocess import load_PubMed_link
load_PubMed_link()
PY
# Yelp（链接预测）
python - <<'PY'
from preprocess.data_preprocess import load_Yelp
load_Yelp()
PY
# Freebase（节点分类）
python - <<'PY'
from preprocess.data_preprocess import load_Freebase
load_Freebase()
PY
```

---

## 快速开始

### 链接预测（LP）
- DBLP（作者-作者）：
```bash
python scripts/link_DBLP.py --model nars  --device 0 --epochs 50 --num_parts 100
# 分布式（2卡）
torchrun --nproc_per_node=2 scripts/link_DBLP.py --model nars --epochs 50 --num_parts 100
```
- PubMed（疾病-疾病）：
```bash
python scripts/link_PubMed.py --model nars  --device 0 --epochs 50 --num_parts 100
```
- Yelp（商户-短语）：
```bash
python scripts/link_Yelp.py --model sehgnn --device 0 --epochs 50 --num_parts 100
```
常用参数：
- --model [nars|sehgnn]
- --hidden_channel 256（默认）
- --dropout 0.1，--lr 1e-3，--batch_size 1000，--epochs 300（可调）
- --save_path ./partition（默认）
- --num_parts 100（划分份数，影响子图规模与速度）

说明：首次运行会在 partition/ 下生成“簇/影响节点”缓存 pkl，加速后续运行。

### 节点分类（NC）
- DBLP 作者分类（默认 RGAT；RGAT/RGCN 在脚本中走 CPU 训练路径）：
```bash
python -m torch.distributed.launch --nproc_per_node=2 scripts/classify_DBLP.py --model rgat --epochs 100 --num_parts 100
```
- Freebase 实体分类：
```bash
python scripts/classify_Freebase.py --model rhgnn --epochs 100 --num_parts 100
```
- OGB-MAG 论文分类：
```bash
python scripts/classify_Ogbn.py --model nars --epochs 100 --num_parts 100
```

### 消融实验
```bash
bash scripts/run_ablation_experiments.sh
```

---

## 关键设计与实现要点
- 划分与补全：utils.cluster / global_clusters 基于 Metis 对语义子图划分，并按度数补全影响节点
- 子图数据集：utils.ClusterDataset 支持以子图为样本的训练/传播；提供 OptimizedClusterDataset 以磁盘缓存加速
- 传播-学习解耦（LP）：先对子图做一次性消息传递得到 full_emb，再在边对上训练轻量判别头（LinkPredictor）
- DDP 支持：脚本自动读取 LOCAL_RANK；传播阶段按子图索引分摊并 all_reduce 聚合

---

## 常见问题（FAQ）
- DGL/Torch 与 CUDA 版本不匹配：请参照 requirements.txt 注释，替换为与本机 CUDA 匹配的 whl 索引
- CUDA OOM：降低 hidden_channel/batch_size；或先在 CPU 检验流程
- 进程卡主/死锁：确保使用 torchrun 启动；多进程写入缓存时已加锁保护，长时间等待可清理 partition/ 与缓存后重试
- 数据读取失败：检查 dataset/* 下 .dat 文件是否齐全、编码为 UTF-8，无额外空行/空字段

---
