import torch
from torch import nn
import torch.nn.functional as F
import dgl.function as fn
import dgl
from .common import FeedForwardNet, FeatAttention


class RHGNN_layer(nn.Module):  # 定义关系异构图神经网络层
    def __init__(self,
                 out_feats,  # 输出特征维度
                 num_heads,  # 多头注意力的头数
                 num_etypes,  # 边类型数量
                 attn_drop=0.2):  # 注意力dropout率
        super(RHGNN_layer, self).__init__()  # 调用父类初始化方法
        self._num_heads = num_heads  # 设置头数
        self._out_feats = out_feats  # 设置输出特征维度
        self._num_etypes = num_etypes  # 设置边类型数量

        # 关系特定的变换矩阵
        self.relation_transforms = nn.ModuleList([
            nn.Linear(out_feats * num_heads, out_feats * num_heads, bias=False)
            for _ in range(num_etypes)
        ])

        # 异构图注意力机制
        self.hetero_attn = nn.ModuleList([
            nn.MultiheadAttention(out_feats * num_heads, num_heads, dropout=attn_drop, batch_first=True)
            for _ in range(num_etypes)
        ])

        # 关系权重学习
        self.relation_weights = nn.Parameter(torch.ones(num_etypes))
        self.attn_dropout = nn.Dropout(attn_drop)
        self.layer_norm = nn.LayerNorm(out_feats * num_heads)

        self.reset_parameters()  # 重置参数

    def reset_parameters(self):  # 参数重置方法
        gain = nn.init.calculate_gain('relu')  # 计算ReLU激活函数的增益值
        for transform in self.relation_transforms:
            nn.init.xavier_uniform_(transform.weight, gain=gain)
        nn.init.uniform_(self.relation_weights, 0.1, 1.0)

    def forward(self, graph, feat):  # 前向传播方法
        with graph.local_scope():  # 使用局部作用域
            feat_reshaped = feat.view(-1, self._num_heads * self._out_feats)
            relation_feats = []  # 存储每种关系的特征

            for i in range(self._num_etypes):  # 遍历每种边类型
                mask = graph.edata["_TYPE"] == i  # 创建当前边类型的掩码
                subgraph = graph.edge_subgraph(mask, preserve_nodes=True)  # 根据掩码创建子图

                if subgraph.num_edges() == 0:  # 如果没有该类型的边
                    relation_feats.append(torch.zeros_like(feat_reshaped))
                    continue

                # 应用关系特定的变换
                transformed_feat = self.relation_transforms[i](feat_reshaped)
                subgraph.ndata["feat"] = transformed_feat

                # 消息传递和聚合
                subgraph.update_all(fn.copy_u("feat", "msg"),
                                    fn.mean("msg", "feat_neighbor"))

                neighbor_feat = subgraph.ndata.pop("feat_neighbor")

                # 应用异构图注意力
                # 重塑为 (batch_size, seq_len, embed_dim) 用于注意力计算
                query = transformed_feat.unsqueeze(1)  # (N, 1, D)
                key = neighbor_feat.unsqueeze(1)  # (N, 1, D)
                value = neighbor_feat.unsqueeze(1)  # (N, 1, D)

                attn_out, _ = self.hetero_attn[i](query, key, value)
                attn_out = attn_out.squeeze(1)  # (N, D)

                relation_feats.append(attn_out)

            # 使用可学习的关系权重进行加权聚合
            relation_weights_norm = F.softmax(self.relation_weights, dim=0)
            weighted_feat = sum(w * feat for w, feat in zip(relation_weights_norm, relation_feats))

            # 残差连接和层归一化
            feat_out = self.layer_norm(weighted_feat + feat_reshaped)
            feat_out = F.relu(feat_out)  # 应用ReLU激活函数
            return feat_out  # 返回处理后的特征


class RHGNN(nn.Module):  # 定义关系异构图神经网络模型
    def __init__(self, in_feats, hidden, out_feats, dropout, input_dropout, num_layers, num_etypes, num_heads):  # 初始化方法
        super(RHGNN, self).__init__()  # 调用父类初始化方法
        self.num_layers = num_layers  # 设置层数
        self.out_feats = out_feats  # 设置输出特征维度
        self.num_heads = num_heads  # 设置头数
        self.hidden = hidden  # 设置隐藏层维度
        self.input_dropout = nn.Dropout(input_dropout)  # 创建输入dropout层
        self.dropout = nn.Dropout(dropout)  # 创建常规dropout层
        self.rhgnn_layers = nn.ModuleList()  # 创建RHGNN层列表
        for _ in range(num_layers):  # 创建所有RHGNN层
            self.rhgnn_layers.append(RHGNN_layer(hidden // num_heads, num_heads, num_etypes))

        self.mlp = nn.Sequential(  # 创建多层感知机
            nn.Linear(hidden, hidden),  # 第一个线性层
            nn.ReLU(),  # ReLU激活函数
            nn.Dropout(dropout),  # Dropout层
            nn.Linear(hidden, hidden),  # 第二个线性层
            nn.ReLU(),  # ReLU激活函数
            nn.Dropout(dropout),  # Dropout层
            nn.Linear(hidden, out_feats)  # 输出线性层
        )
        self.linear = nn.Linear(in_feats, hidden)  # 输入特征转换线性层
        self.label_linear = nn.Linear(out_feats, hidden)  # 标签特征转换线性层
        self.norms = nn.ModuleList([nn.BatchNorm1d(hidden) for _ in range(num_layers + 1)])  # 创建批归一化层列表
        self.inception_ffs = nn.ModuleList()  # 创建Inception风格前馈网络列表
        for _ in range(num_layers + 1):  # 为每一跳创建前馈网络
            self.inception_ffs.append(
                FeedForwardNet(hidden, hidden, hidden, 1, dropout))  # 添加前馈网络


    def forward(self, graph, feat, label_feat):  # 前向传播方法
        feat = self.linear(self.input_dropout(feat))  # 应用输入dropout并转换输入特征
        label_feat = self.label_linear(self.input_dropout(label_feat))  # 应用输入dropout并转换标签特征
        feat += label_feat  # 将特征和标签特征相加
        feat_hop = [feat]  # 初始化特征跳跃连接列表

        for i in range(self.num_layers):  # 遍历每一层
            graph.ndata["feat"] = feat  # 设置图节点特征
            feat = self.rhgnn_layers[i](graph, feat)  # 通过RHGNN层处理特征
            feat_hop.append(feat)  # 添加处理后的特征到跳跃连接列表
        feat_hop = [self.input_dropout(feat_h) for feat_h in feat_hop]  # 对每层的特征应用输入dropout

        norm_feats = []  # 初始化归一化特征列表
        for _feat, ff in zip(feat_hop, self.norms):  # 遍历特征和归一化层
            norm_feats.append(ff(_feat))  # 应用批归一化并添加到列表

        tmp = []  # 初始化临时特征列表
        for _feat, _ff in zip(norm_feats, self.inception_ffs):  # 遍历归一化特征和前馈网络
            tmp.append(_ff(_feat).view(-1, self.num_heads, self.hidden // self.num_heads))  # 应用前馈网络并重塑
        feat = self.dropout((sum(tmp) / len(tmp)).view(-1, self.hidden))  # 计算平均值，重塑并应用dropout
        return self.mlp(feat)  # 通过MLP处理并返回最终特征