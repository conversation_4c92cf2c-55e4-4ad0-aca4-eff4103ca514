一、Ogbn-mag数据集
1.rgcn  通过图传播计算嵌入
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
loading data costs 6.7319s
loading data costs 6.7553s
Convert a graph into a bidirected graph: 7.971 seconds, peak memory: 40.945 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 40.945 GB
[10:09:45] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 42182144 edges into 100 parts and get 9776177 edge cuts
Metis partitioning: 20.607 seconds, peak memory: 42.350 GB
Split the graph: 8.479 seconds
Construct subgraphs: 0.044 seconds
clustering costs 47.3179s
Convert a graph into a bidirected graph: 19.774 seconds, peak memory: 45.499 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 45.499 GB
[10:11:05] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 97322811 edges into 100 parts and get 21708196 edge cuts
Metis partitioning: 38.154 seconds, peak memory: 47.953 GB
Split the graph: 18.387 seconds
Construct subgraphs: 0.053 seconds
clustering costs 88.6574s
Convert a graph into a bidirected graph: 92.389 seconds, peak memory: 67.419 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 67.419 GB
[10:16:06] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 396446017 edges into 100 parts and get 101190073 edge cuts
Metis partitioning: 166.090 seconds, peak memory: 78.163 GB
Split the graph: 73.535 seconds
Construct subgraphs: 0.103 seconds
clustering costs 356.3540s
Convert a graph into a bidirected graph: 7.849 seconds, peak memory: 78.163 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 78.163 GB
[10:18:10] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 42409796 edges into 100 parts and get 9489414 edge cuts
Metis partitioning: 25.188 seconds, peak memory: 78.163 GB
Split the graph: 8.656 seconds
Construct subgraphs: 0.045 seconds
clustering costs 56.3667s
Convert a graph into a bidirected graph: 76.157 seconds, peak memory: 78.163 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 78.163 GB
[10:21:38] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 404098595 edges into 100 parts and get 109735043 edge cuts
Metis partitioning: 100.051 seconds, peak memory: 78.440 GB
Split the graph: 81.963 seconds
Construct subgraphs: 0.080 seconds
clustering costs 280.7792s
Convert a graph into a bidirected graph: 9.170 seconds, peak memory: 78.440 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 78.440 GB
[10:23:49] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 51963221 edges into 100 parts and get 14065195 edge cuts
Metis partitioning: 25.125 seconds, peak memory: 78.440 GB
Split the graph: 10.089 seconds
Construct subgraphs: 0.043 seconds
clustering costs 57.4400s
num of nodes in each cluster:19333
num of nodes in each influential nodes:100
allocate time:1123.7864s

(pytorch) zw@node18:~/code/test/SubInfer/scripts$ torchrun --standalone --nproc_per_node=2 classify_Ogbn.py --model rgcn
WARNING:torch.distributed.run:
*****************************************
Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
*****************************************
Namespace(local_rank=0, device=0, num_layer=3, hidden_channel=512, dropout=0.5, lr=0.001, epochs=300, batch_size=25000, runs=1, save_path='../partition', dataset='ogbn-mag', num_parts=100, mask=0.5, supply_rate=0.1, use_emb=False, model='rgcn', all_models=False)
Namespace(local_rank=1, device=0, num_layer=3, hidden_channel=512, dropout=0.5, lr=0.001, epochs=300, batch_size=25000, runs=1, save_path='../partition', dataset='ogbn-mag', num_parts=100, mask=0.5, supply_rate=0.1, use_emb=False, model='rgcn', all_models=False)
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
loading data costs 9.9720s
loading data costs 10.0361s
Found existing partition file at ../partition/mag-rgcn-partition100.pkl, skip computing.
tensor([     55,     167,     658,  ..., 1101294, 1197811, 1772089])
tensor([     55,     167,     658,  ..., 1101294, 1197811, 1772089])
training
training
RGCN(
  (input_dropout): Dropout(p=0.0, inplace=False)
  (dropout): Dropout(p=0.5, inplace=False)
  (rgat): ModuleList(
    (0): RGCN_layer()
    (1): RGCN_layer()
    (2): RGCN_layer()
  )
  (mlp): Sequential(
    (0): Linear(in_features=512, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.5, inplace=False)
    (3): Linear(in_features=512, out_features=512, bias=True)
    (4): ReLU()
    (5): Dropout(p=0.5, inplace=False)
    (6): Linear(in_features=512, out_features=349, bias=True)
  )
  (linear): Linear(in_features=128, out_features=512, bias=True)
  (label_linear): Linear(in_features=349, out_features=512, bias=True)
  (norms): ModuleList(
    (0): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (inception_ffs): ModuleList(
    (0): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (1): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (2): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
  )
)
RGCN(
  (input_dropout): Dropout(p=0.0, inplace=False)
  (dropout): Dropout(p=0.5, inplace=False)
  (rgat): ModuleList(
    (0): RGCN_layer()
    (1): RGCN_layer()
    (2): RGCN_layer()
  )
  (mlp): Sequential(
    (0): Linear(in_features=512, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.5, inplace=False)
    (3): Linear(in_features=512, out_features=512, bias=True)
    (4): ReLU()
    (5): Dropout(p=0.5, inplace=False)
    (6): Linear(in_features=512, out_features=349, bias=True)
  )
  (linear): Linear(in_features=128, out_features=512, bias=True)
  (label_linear): Linear(in_features=349, out_features=512, bias=True)
  (norms): ModuleList(
    (0): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (inception_ffs): ModuleList(
    (0): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (1): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (2): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
  )
)
# Params: 1740637
Using device: cuda:1
# Params: 1740637
Using device: cuda:0
[W reducer.cpp:1251] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())
[W reducer.cpp:1251] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())
Epoch 1, Training Time(s): 16.1344, Inference Time(s): 16.7927,Acc: Train 0.3583, Val 0.0810, Test 0.0917
Best Epoch 1, Valid 0.0810, Test 0.0917
Epoch 2, Training Time(s): 18.4578, Inference Time(s): 19.5151,Acc: Train 0.5315, Val 0.0984, Test 0.1183
Best Epoch 2, Valid 0.0984, Test 0.1183
Epoch 3, Training Time(s): 17.8184, Inference Time(s): 20.4047,Acc: Train 0.6617, Val 0.1773, Test 0.1902
Best Epoch 3, Valid 0.1773, Test 0.1902
Epoch 4, Training Time(s): 17.0922, Inference Time(s): 17.1340,Acc: Train 0.7031, Val 0.2212, Test 0.2376
Best Epoch 4, Valid 0.2212, Test 0.2376
Epoch 5, Training Time(s): 19.5478, Inference Time(s): 17.9665,Acc: Train 0.7088, Val 0.2331, Test 0.2564
Best Epoch 5, Valid 0.2331, Test 0.2564
Epoch 6, Training Time(s): 16.4664, Inference Time(s): 16.3077,Acc: Train 0.7282, Val 0.2628, Test 0.2830
Best Epoch 6, Valid 0.2628, Test 0.2830
Epoch 7, Training Time(s): 15.0397, Inference Time(s): 16.8916,Acc: Train 0.7363, Val 0.2787, Test 0.2891
Best Epoch 7, Valid 0.2787, Test 0.2891
Epoch 8, Training Time(s): 15.0821, Inference Time(s): 16.1326,Acc: Train 0.7411, Val 0.2722, Test 0.2838
Best Epoch 7, Valid 0.2787, Test 0.2891
Epoch 9, Training Time(s): 15.4500, Inference Time(s): 16.6725,Acc: Train 0.7459, Val 0.3007, Test 0.3103
Best Epoch 9, Valid 0.3007, Test 0.3103
Epoch 10, Training Time(s): 14.7981, Inference Time(s): 16.0927,Acc: Train 0.7490, Val 0.3127, Test 0.3187
Best Epoch 10, Valid 0.3127, Test 0.3187
Epoch 11, Training Time(s): 14.8985, Inference Time(s): 16.2443,Acc: Train 0.7518, Val 0.3219, Test 0.3271
Best Epoch 11, Valid 0.3219, Test 0.3271
Epoch 12, Training Time(s): 15.2423, Inference Time(s): 16.6572,Acc: Train 0.7528, Val 0.3222, Test 0.3300
Best Epoch 12, Valid 0.3222, Test 0.3300
Epoch 13, Training Time(s): 15.3635, Inference Time(s): 16.9381,Acc: Train 0.7542, Val 0.3319, Test 0.3419
Best Epoch 13, Valid 0.3319, Test 0.3419
Epoch 14, Training Time(s): 17.5475, Inference Time(s): 16.0747,Acc: Train 0.7544, Val 0.3375, Test 0.3459
Best Epoch 14, Valid 0.3375, Test 0.3459
Epoch 15, Training Time(s): 15.3076, Inference Time(s): 16.4120,Acc: Train 0.7576, Val 0.3475, Test 0.3552
Best Epoch 15, Valid 0.3475, Test 0.3552
Epoch 16, Training Time(s): 17.1738, Inference Time(s): 16.2843,Acc: Train 0.7604, Val 0.3511, Test 0.3572
Best Epoch 16, Valid 0.3511, Test 0.3572
Epoch 17, Training Time(s): 15.3847, Inference Time(s): 16.1303,Acc: Train 0.7632, Val 0.3578, Test 0.3623
Best Epoch 17, Valid 0.3578, Test 0.3623
Epoch 18, Training Time(s): 15.5277, Inference Time(s): 16.4555,Acc: Train 0.7643, Val 0.3572, Test 0.3640
Best Epoch 17, Valid 0.3578, Test 0.3623
Epoch 19, Training Time(s): 15.1954, Inference Time(s): 16.1803,Acc: Train 0.7656, Val 0.3590, Test 0.3641
Best Epoch 19, Valid 0.3590, Test 0.3641
Epoch 20, Training Time(s): 15.5350, Inference Time(s): 16.0913,Acc: Train 0.7685, Val 0.3636, Test 0.3707
Best Epoch 20, Valid 0.3636, Test 0.3707
Epoch 21, Training Time(s): 15.0560, Inference Time(s): 16.7441,Acc: Train 0.7681, Val 0.3658, Test 0.3702
Best Epoch 21, Valid 0.3658, Test 0.3702
Epoch 22, Training Time(s): 14.7231, Inference Time(s): 16.6151,Acc: Train 0.7693, Val 0.3669, Test 0.3714
Best Epoch 22, Valid 0.3669, Test 0.3714
Epoch 23, Training Time(s): 15.0222, Inference Time(s): 16.6658,Acc: Train 0.7696, Val 0.3646, Test 0.3713
Best Epoch 22, Valid 0.3669, Test 0.3714
Epoch 24, Training Time(s): 17.1333, Inference Time(s): 15.9668,Acc: Train 0.7702, Val 0.3708, Test 0.3749
Best Epoch 24, Valid 0.3708, Test 0.3749
Epoch 25, Training Time(s): 14.8243, Inference Time(s): 16.2884,Acc: Train 0.7702, Val 0.3707, Test 0.3780
Best Epoch 24, Valid 0.3708, Test 0.3749
Epoch 26, Training Time(s): 14.9569, Inference Time(s): 16.5623,Acc: Train 0.7718, Val 0.3794, Test 0.3827
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 27, Training Time(s): 14.7969, Inference Time(s): 16.5223,Acc: Train 0.7728, Val 0.3734, Test 0.3771
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 28, Training Time(s): 14.7863, Inference Time(s): 16.5309,Acc: Train 0.7733, Val 0.3776, Test 0.3805
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 29, Training Time(s): 14.8875, Inference Time(s): 16.4097,Acc: Train 0.7735, Val 0.3698, Test 0.3770
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 30, Training Time(s): 15.0662, Inference Time(s): 16.7642,Acc: Train 0.7750, Val 0.3690, Test 0.3733
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 31, Training Time(s): 18.3120, Inference Time(s): 16.2629,Acc: Train 0.7754, Val 0.3708, Test 0.3724
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 32, Training Time(s): 15.4699, Inference Time(s): 16.5746,Acc: Train 0.7771, Val 0.3767, Test 0.3787
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 33, Training Time(s): 14.9189, Inference Time(s): 16.5263,Acc: Train 0.7771, Val 0.3791, Test 0.3825
Best Epoch 26, Valid 0.3794, Test 0.3827
Epoch 34, Training Time(s): 15.5620, Inference Time(s): 16.5388,Acc: Train 0.7770, Val 0.3802, Test 0.3841
Best Epoch 34, Valid 0.3802, Test 0.3841
Epoch 35, Training Time(s): 15.4424, Inference Time(s): 16.5955,Acc: Train 0.7773, Val 0.3781, Test 0.3836
Best Epoch 34, Valid 0.3802, Test 0.3841
Epoch 36, Training Time(s): 15.0063, Inference Time(s): 16.6028,Acc: Train 0.7773, Val 0.3675, Test 0.3689
Best Epoch 34, Valid 0.3802, Test 0.3841
Epoch 37, Training Time(s): 15.4867, Inference Time(s): 16.0187,Acc: Train 0.7778, Val 0.3746, Test 0.3730
Best Epoch 34, Valid 0.3802, Test 0.3841
Epoch 38, Training Time(s): 15.1330, Inference Time(s): 16.5368,Acc: Train 0.7786, Val 0.3724, Test 0.3723
Best Epoch 34, Valid 0.3802, Test 0.3841
Epoch 39, Training Time(s): 15.6484, Inference Time(s): 16.4901,Acc: Train 0.7790, Val 0.3831, Test 0.3833
Best Epoch 39, Valid 0.3831, Test 0.3833
Epoch 40, Training Time(s): 15.1635, Inference Time(s): 16.2824,Acc: Train 0.7795, Val 0.3804, Test 0.3813
Best Epoch 39, Valid 0.3831, Test 0.3833
Epoch 41, Training Time(s): 15.4117, Inference Time(s): 16.3315,Acc: Train 0.7811, Val 0.3876, Test 0.3878
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 42, Training Time(s): 15.5221, Inference Time(s): 16.3652,Acc: Train 0.7809, Val 0.3732, Test 0.3734
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 43, Training Time(s): 17.6964, Inference Time(s): 16.6169,Acc: Train 0.7809, Val 0.3812, Test 0.3861
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 44, Training Time(s): 15.0787, Inference Time(s): 16.0240,Acc: Train 0.7810, Val 0.3687, Test 0.3703
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 45, Training Time(s): 15.3241, Inference Time(s): 16.6128,Acc: Train 0.7807, Val 0.3862, Test 0.3870
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 46, Training Time(s): 14.8767, Inference Time(s): 16.7278,Acc: Train 0.7794, Val 0.3605, Test 0.3620
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 47, Training Time(s): 15.1634, Inference Time(s): 16.1103,Acc: Train 0.7813, Val 0.3771, Test 0.3809
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 48, Training Time(s): 15.1704, Inference Time(s): 16.1954,Acc: Train 0.7814, Val 0.3675, Test 0.3708
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 49, Training Time(s): 15.2512, Inference Time(s): 16.7151,Acc: Train 0.7810, Val 0.3821, Test 0.3847
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 50, Training Time(s): 15.1461, Inference Time(s): 16.1858,Acc: Train 0.7820, Val 0.3798, Test 0.3791
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 51, Training Time(s): 15.0125, Inference Time(s): 16.6452,Acc: Train 0.7811, Val 0.3836, Test 0.3840
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 52, Training Time(s): 15.2898, Inference Time(s): 16.5633,Acc: Train 0.7823, Val 0.3753, Test 0.3745
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 53, Training Time(s): 15.1897, Inference Time(s): 16.4718,Acc: Train 0.7808, Val 0.3787, Test 0.3800
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 54, Training Time(s): 15.1376, Inference Time(s): 16.5480,Acc: Train 0.7822, Val 0.3724, Test 0.3719
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 55, Training Time(s): 15.0527, Inference Time(s): 15.9432,Acc: Train 0.7794, Val 0.3664, Test 0.3658
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 56, Training Time(s): 15.4900, Inference Time(s): 16.0938,Acc: Train 0.7821, Val 0.3732, Test 0.3720
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 57, Training Time(s): 14.9311, Inference Time(s): 16.7981,Acc: Train 0.7825, Val 0.3761, Test 0.3762
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 58, Training Time(s): 16.2886, Inference Time(s): 16.7574,Acc: Train 0.7826, Val 0.3727, Test 0.3748
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 59, Training Time(s): 16.0305, Inference Time(s): 16.4484,Acc: Train 0.7821, Val 0.3781, Test 0.3798
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 60, Training Time(s): 15.8592, Inference Time(s): 15.8751,Acc: Train 0.7834, Val 0.3752, Test 0.3770
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 61, Training Time(s): 15.3314, Inference Time(s): 16.6297,Acc: Train 0.7843, Val 0.3839, Test 0.3887
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 62, Training Time(s): 15.3550, Inference Time(s): 16.7264,Acc: Train 0.7846, Val 0.3822, Test 0.3838
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 63, Training Time(s): 14.7650, Inference Time(s): 16.2625,Acc: Train 0.7847, Val 0.3846, Test 0.3892
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 64, Training Time(s): 15.5895, Inference Time(s): 15.9344,Acc: Train 0.7849, Val 0.3863, Test 0.3878
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 65, Training Time(s): 14.7284, Inference Time(s): 16.1207,Acc: Train 0.7821, Val 0.3867, Test 0.3894
Best Epoch 41, Valid 0.3876, Test 0.3878
Epoch 66, Training Time(s): 15.4095, Inference Time(s): 16.8023,Acc: Train 0.7827, Val 0.3889, Test 0.3924
Best Epoch 66, Valid 0.3889, Test 0.3924
Epoch 67, Training Time(s): 15.4840, Inference Time(s): 16.1557,Acc: Train 0.7818, Val 0.3885, Test 0.3927
Best Epoch 66, Valid 0.3889, Test 0.3924
Epoch 68, Training Time(s): 15.5270, Inference Time(s): 16.7108,Acc: Train 0.7845, Val 0.3915, Test 0.3924
Best Epoch 68, Valid 0.3915, Test 0.3924
Epoch 69, Training Time(s): 15.8741, Inference Time(s): 16.0016,Acc: Train 0.7829, Val 0.3839, Test 0.3833
Best Epoch 68, Valid 0.3915, Test 0.3924
Epoch 70, Training Time(s): 14.8363, Inference Time(s): 16.6917,Acc: Train 0.7847, Val 0.3965, Test 0.3960
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 71, Training Time(s): 14.9907, Inference Time(s): 16.5778,Acc: Train 0.7843, Val 0.3833, Test 0.3846
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 72, Training Time(s): 14.6581, Inference Time(s): 15.9287,Acc: Train 0.7850, Val 0.3892, Test 0.3907
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 73, Training Time(s): 14.9490, Inference Time(s): 16.0690,Acc: Train 0.7855, Val 0.3892, Test 0.3907
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 74, Training Time(s): 14.8404, Inference Time(s): 16.6719,Acc: Train 0.7855, Val 0.3861, Test 0.3879
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 75, Training Time(s): 14.7513, Inference Time(s): 16.4004,Acc: Train 0.7827, Val 0.3785, Test 0.3873
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 76, Training Time(s): 15.0062, Inference Time(s): 16.3228,Acc: Train 0.7845, Val 0.3882, Test 0.3910
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 77, Training Time(s): 14.8481, Inference Time(s): 16.4305,Acc: Train 0.7836, Val 0.3862, Test 0.3862
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 78, Training Time(s): 17.4307, Inference Time(s): 16.8223,Acc: Train 0.7831, Val 0.3841, Test 0.3822
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 79, Training Time(s): 15.3882, Inference Time(s): 16.2737,Acc: Train 0.7835, Val 0.3837, Test 0.3841
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 80, Training Time(s): 14.8526, Inference Time(s): 16.7752,Acc: Train 0.7842, Val 0.3747, Test 0.3719
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 81, Training Time(s): 14.9240, Inference Time(s): 16.3387,Acc: Train 0.7847, Val 0.3813, Test 0.3778
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 82, Training Time(s): 15.0253, Inference Time(s): 16.8228,Acc: Train 0.7846, Val 0.3734, Test 0.3690
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 83, Training Time(s): 15.5575, Inference Time(s): 16.9168,Acc: Train 0.7840, Val 0.3836, Test 0.3767
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 84, Training Time(s): 15.4677, Inference Time(s): 16.5726,Acc: Train 0.7840, Val 0.3753, Test 0.3735
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 85, Training Time(s): 15.0059, Inference Time(s): 16.6821,Acc: Train 0.7827, Val 0.3787, Test 0.3777
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 86, Training Time(s): 15.0677, Inference Time(s): 16.5095,Acc: Train 0.7828, Val 0.3827, Test 0.3773
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 87, Training Time(s): 15.5045, Inference Time(s): 16.7550,Acc: Train 0.7853, Val 0.3846, Test 0.3856
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 88, Training Time(s): 15.1315, Inference Time(s): 16.7790,Acc: Train 0.7856, Val 0.3843, Test 0.3868
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 89, Training Time(s): 14.9393, Inference Time(s): 16.6190,Acc: Train 0.7858, Val 0.3915, Test 0.3947
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 90, Training Time(s): 14.8045, Inference Time(s): 16.6511,Acc: Train 0.7852, Val 0.3784, Test 0.3790
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 91, Training Time(s): 14.9184, Inference Time(s): 16.3396,Acc: Train 0.7865, Val 0.3851, Test 0.3849
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 92, Training Time(s): 14.8863, Inference Time(s): 16.4155,Acc: Train 0.7868, Val 0.3923, Test 0.3927
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 93, Training Time(s): 15.2904, Inference Time(s): 16.5530,Acc: Train 0.7863, Val 0.3947, Test 0.3967
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 94, Training Time(s): 15.1222, Inference Time(s): 16.3786,Acc: Train 0.7886, Val 0.3943, Test 0.3952
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 95, Training Time(s): 14.9049, Inference Time(s): 16.1389,Acc: Train 0.7868, Val 0.3920, Test 0.3904
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 96, Training Time(s): 15.1573, Inference Time(s): 16.0166,Acc: Train 0.7862, Val 0.3809, Test 0.3757
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 97, Training Time(s): 14.6999, Inference Time(s): 16.3890,Acc: Train 0.7849, Val 0.3662, Test 0.3611
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 98, Training Time(s): 17.4234, Inference Time(s): 16.6125,Acc: Train 0.7864, Val 0.3783, Test 0.3761
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 99, Training Time(s): 15.0997, Inference Time(s): 16.5360,Acc: Train 0.7864, Val 0.3844, Test 0.3855
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 100, Training Time(s): 14.7841, Inference Time(s): 15.9709,Acc: Train 0.7879, Val 0.3811, Test 0.3820
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 101, Training Time(s): 17.4408, Inference Time(s): 16.2855,Acc: Train 0.7861, Val 0.3678, Test 0.3741
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 102, Training Time(s): 14.9607, Inference Time(s): 16.2498,Acc: Train 0.7847, Val 0.3706, Test 0.3737
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 103, Training Time(s): 14.8493, Inference Time(s): 16.5861,Acc: Train 0.7876, Val 0.3878, Test 0.3889
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 104, Training Time(s): 14.6020, Inference Time(s): 16.4510,Acc: Train 0.7873, Val 0.3798, Test 0.3824
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 105, Training Time(s): 15.5202, Inference Time(s): 16.5355,Acc: Train 0.7877, Val 0.3696, Test 0.3687
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 106, Training Time(s): 14.7230, Inference Time(s): 16.4394,Acc: Train 0.7870, Val 0.3811, Test 0.3829
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 107, Training Time(s): 15.0060, Inference Time(s): 16.6896,Acc: Train 0.7887, Val 0.3776, Test 0.3769
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 108, Training Time(s): 14.9120, Inference Time(s): 16.4906,Acc: Train 0.7883, Val 0.3820, Test 0.3810
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 109, Training Time(s): 15.3619, Inference Time(s): 16.6384,Acc: Train 0.7883, Val 0.3813, Test 0.3786
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 110, Training Time(s): 14.8315, Inference Time(s): 15.9245,Acc: Train 0.7885, Val 0.3844, Test 0.3791
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 111, Training Time(s): 15.4156, Inference Time(s): 16.1339,Acc: Train 0.7883, Val 0.3898, Test 0.3882
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 112, Training Time(s): 15.5428, Inference Time(s): 16.4782,Acc: Train 0.7895, Val 0.3886, Test 0.3852
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 113, Training Time(s): 15.0381, Inference Time(s): 16.5158,Acc: Train 0.7895, Val 0.3839, Test 0.3827
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 114, Training Time(s): 15.1737, Inference Time(s): 16.4868,Acc: Train 0.7893, Val 0.3830, Test 0.3831
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 115, Training Time(s): 15.2096, Inference Time(s): 16.3880,Acc: Train 0.7898, Val 0.3777, Test 0.3772
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 116, Training Time(s): 15.1940, Inference Time(s): 16.6021,Acc: Train 0.7884, Val 0.3728, Test 0.3751
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 117, Training Time(s): 14.9397, Inference Time(s): 16.5794,Acc: Train 0.7874, Val 0.3765, Test 0.3775
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 118, Training Time(s): 15.7962, Inference Time(s): 16.3355,Acc: Train 0.7878, Val 0.3818, Test 0.3863
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 119, Training Time(s): 15.5037, Inference Time(s): 16.4670,Acc: Train 0.7885, Val 0.3790, Test 0.3765
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 120, Training Time(s): 15.3254, Inference Time(s): 16.3681,Acc: Train 0.7879, Val 0.3897, Test 0.3886
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 121, Training Time(s): 15.2523, Inference Time(s): 15.9432,Acc: Train 0.7884, Val 0.3774, Test 0.3723
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 122, Training Time(s): 15.0566, Inference Time(s): 15.9425,Acc: Train 0.7887, Val 0.3871, Test 0.3836
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 123, Training Time(s): 15.8076, Inference Time(s): 16.3551,Acc: Train 0.7896, Val 0.3824, Test 0.3800
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 124, Training Time(s): 14.9598, Inference Time(s): 16.6087,Acc: Train 0.7904, Val 0.3812, Test 0.3765
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 125, Training Time(s): 14.6183, Inference Time(s): 16.6610,Acc: Train 0.7879, Val 0.3671, Test 0.3629
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 126, Training Time(s): 14.8231, Inference Time(s): 16.5543,Acc: Train 0.7903, Val 0.3833, Test 0.3845
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 127, Training Time(s): 15.2561, Inference Time(s): 16.4896,Acc: Train 0.7880, Val 0.3810, Test 0.3845
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 128, Training Time(s): 15.0419, Inference Time(s): 16.4296,Acc: Train 0.7900, Val 0.3849, Test 0.3843
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 129, Training Time(s): 15.0353, Inference Time(s): 16.7149,Acc: Train 0.7900, Val 0.3918, Test 0.3930
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 130, Training Time(s): 14.7893, Inference Time(s): 16.4200,Acc: Train 0.7897, Val 0.3814, Test 0.3833
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 131, Training Time(s): 15.0862, Inference Time(s): 16.4787,Acc: Train 0.7903, Val 0.3865, Test 0.3858
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 132, Training Time(s): 15.0122, Inference Time(s): 16.0647,Acc: Train 0.7909, Val 0.3775, Test 0.3752
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 133, Training Time(s): 15.0306, Inference Time(s): 16.2646,Acc: Train 0.7895, Val 0.3786, Test 0.3794
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 134, Training Time(s): 15.7036, Inference Time(s): 16.5513,Acc: Train 0.7897, Val 0.3841, Test 0.3788
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 135, Training Time(s): 15.1598, Inference Time(s): 16.5681,Acc: Train 0.7891, Val 0.3768, Test 0.3776
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 136, Training Time(s): 15.2605, Inference Time(s): 16.5544,Acc: Train 0.7887, Val 0.3911, Test 0.3876
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 137, Training Time(s): 14.9366, Inference Time(s): 16.7371,Acc: Train 0.7881, Val 0.3867, Test 0.3867
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 138, Training Time(s): 15.1356, Inference Time(s): 16.4967,Acc: Train 0.7889, Val 0.3910, Test 0.3907
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 139, Training Time(s): 15.0179, Inference Time(s): 16.0471,Acc: Train 0.7875, Val 0.3907, Test 0.3896
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 140, Training Time(s): 15.2065, Inference Time(s): 16.5097,Acc: Train 0.7867, Val 0.3786, Test 0.3787
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 141, Training Time(s): 16.2822, Inference Time(s): 16.5197,Acc: Train 0.7884, Val 0.3725, Test 0.3728
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 142, Training Time(s): 15.4509, Inference Time(s): 16.3067,Acc: Train 0.7886, Val 0.3829, Test 0.3825
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 143, Training Time(s): 15.1383, Inference Time(s): 16.0369,Acc: Train 0.7855, Val 0.3641, Test 0.3615
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 144, Training Time(s): 14.9782, Inference Time(s): 16.5131,Acc: Train 0.7882, Val 0.3675, Test 0.3655
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 145, Training Time(s): 15.7228, Inference Time(s): 16.9220,Acc: Train 0.7882, Val 0.3812, Test 0.3818
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 146, Training Time(s): 15.3585, Inference Time(s): 16.7887,Acc: Train 0.7877, Val 0.3821, Test 0.3773
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 147, Training Time(s): 14.6874, Inference Time(s): 16.0690,Acc: Train 0.7880, Val 0.3788, Test 0.3768
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 148, Training Time(s): 15.4229, Inference Time(s): 16.5497,Acc: Train 0.7876, Val 0.3685, Test 0.3624
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 149, Training Time(s): 15.6199, Inference Time(s): 16.8008,Acc: Train 0.7867, Val 0.3786, Test 0.3757
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 150, Training Time(s): 15.0967, Inference Time(s): 16.2142,Acc: Train 0.7880, Val 0.3680, Test 0.3631
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 151, Training Time(s): 15.1951, Inference Time(s): 16.0439,Acc: Train 0.7840, Val 0.3639, Test 0.3577
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 152, Training Time(s): 15.5342, Inference Time(s): 16.5304,Acc: Train 0.7863, Val 0.3653, Test 0.3609
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 153, Training Time(s): 15.2765, Inference Time(s): 16.5689,Acc: Train 0.7877, Val 0.3657, Test 0.3569
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 154, Training Time(s): 14.8187, Inference Time(s): 16.3922,Acc: Train 0.7892, Val 0.3674, Test 0.3632
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 155, Training Time(s): 15.0849, Inference Time(s): 16.5650,Acc: Train 0.7888, Val 0.3774, Test 0.3707
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 156, Training Time(s): 14.6537, Inference Time(s): 16.5484,Acc: Train 0.7861, Val 0.3565, Test 0.3550
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 157, Training Time(s): 15.0032, Inference Time(s): 16.4861,Acc: Train 0.7879, Val 0.3750, Test 0.3747
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 158, Training Time(s): 15.2993, Inference Time(s): 16.5939,Acc: Train 0.7894, Val 0.3749, Test 0.3760
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 159, Training Time(s): 15.2893, Inference Time(s): 16.5419,Acc: Train 0.7893, Val 0.3732, Test 0.3736
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 160, Training Time(s): 15.1246, Inference Time(s): 16.3049,Acc: Train 0.7878, Val 0.3668, Test 0.3659
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 161, Training Time(s): 15.0566, Inference Time(s): 16.4231,Acc: Train 0.7897, Val 0.3818, Test 0.3799
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 162, Training Time(s): 14.9055, Inference Time(s): 16.5812,Acc: Train 0.7904, Val 0.3762, Test 0.3729
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 163, Training Time(s): 14.9695, Inference Time(s): 16.5767,Acc: Train 0.7888, Val 0.3824, Test 0.3802
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 164, Training Time(s): 15.5106, Inference Time(s): 16.8022,Acc: Train 0.7908, Val 0.3850, Test 0.3840
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 165, Training Time(s): 15.3842, Inference Time(s): 16.6409,Acc: Train 0.7900, Val 0.3796, Test 0.3746
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 166, Training Time(s): 15.0901, Inference Time(s): 16.6352,Acc: Train 0.7895, Val 0.3820, Test 0.3803
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 167, Training Time(s): 15.4284, Inference Time(s): 16.0510,Acc: Train 0.7892, Val 0.3755, Test 0.3706
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 168, Training Time(s): 15.8838, Inference Time(s): 16.8325,Acc: Train 0.7905, Val 0.3823, Test 0.3788
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 169, Training Time(s): 15.6577, Inference Time(s): 16.6395,Acc: Train 0.7898, Val 0.3835, Test 0.3810
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 170, Training Time(s): 14.8234, Inference Time(s): 16.1133,Acc: Train 0.7895, Val 0.3874, Test 0.3862
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 171, Training Time(s): 14.6960, Inference Time(s): 16.4595,Acc: Train 0.7906, Val 0.3833, Test 0.3826
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 172, Training Time(s): 15.0964, Inference Time(s): 16.4183,Acc: Train 0.7918, Val 0.3954, Test 0.3968
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 173, Training Time(s): 14.7066, Inference Time(s): 16.5697,Acc: Train 0.7916, Val 0.3891, Test 0.3929
Best Epoch 70, Valid 0.3965, Test 0.3960
Epoch 174, Training Time(s): 14.8635, Inference Time(s): 15.8708,Acc: Train 0.7911, Val 0.3966, Test 0.3973
Best Epoch 174, Valid 0.3966, Test 0.3973
Epoch 175, Training Time(s): 15.0985, Inference Time(s): 16.3064,Acc: Train 0.7902, Val 0.3775, Test 0.3788
Best Epoch 174, Valid 0.3966, Test 0.3973
Epoch 176, Training Time(s): 14.7356, Inference Time(s): 16.5071,Acc: Train 0.7895, Val 0.3829, Test 0.3812
Best Epoch 174, Valid 0.3966, Test 0.3973
Epoch 177, Training Time(s): 15.0779, Inference Time(s): 16.7427,Acc: Train 0.7903, Val 0.3849, Test 0.3837
Best Epoch 174, Valid 0.3966, Test 0.3973
Epoch 178, Training Time(s): 15.0598, Inference Time(s): 16.6465,Acc: Train 0.7902, Val 0.3841, Test 0.3830
Best Epoch 174, Valid 0.3966, Test 0.3973
Epoch 179, Training Time(s): 14.8090, Inference Time(s): 16.5781,Acc: Train 0.7892, Val 0.3894, Test 0.3888
Best Epoch 174, Valid 0.3966, Test 0.3973
Epoch 180, Training Time(s): 14.8047, Inference Time(s): 16.4658,Acc: Train 0.7916, Val 0.3955, Test 0.3914
Best Epoch 174, Valid 0.3966, Test 0.3973
Epoch 181, Training Time(s): 17.4032, Inference Time(s): 16.5800,Acc: Train 0.7912, Val 0.3972, Test 0.3924
Best Epoch 181, Valid 0.3972, Test 0.3924
Epoch 182, Training Time(s): 17.6512, Inference Time(s): 16.4363,Acc: Train 0.7867, Val 0.4017, Test 0.4024
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 183, Training Time(s): 15.2378, Inference Time(s): 15.9148,Acc: Train 0.7896, Val 0.3948, Test 0.3930
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 184, Training Time(s): 15.4413, Inference Time(s): 16.0705,Acc: Train 0.7896, Val 0.3996, Test 0.4006
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 185, Training Time(s): 15.5170, Inference Time(s): 16.6895,Acc: Train 0.7895, Val 0.3945, Test 0.3933
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 186, Training Time(s): 14.8774, Inference Time(s): 15.9497,Acc: Train 0.7901, Val 0.3938, Test 0.3924
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 187, Training Time(s): 15.8277, Inference Time(s): 16.4683,Acc: Train 0.7916, Val 0.3944, Test 0.3950
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 188, Training Time(s): 15.0986, Inference Time(s): 16.3796,Acc: Train 0.7907, Val 0.3912, Test 0.3928
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 189, Training Time(s): 15.0702, Inference Time(s): 16.6965,Acc: Train 0.7908, Val 0.3976, Test 0.4001
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 190, Training Time(s): 15.3646, Inference Time(s): 16.3580,Acc: Train 0.7887, Val 0.3807, Test 0.3838
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 191, Training Time(s): 14.9653, Inference Time(s): 16.4673,Acc: Train 0.7912, Val 0.3929, Test 0.3952
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 192, Training Time(s): 15.3621, Inference Time(s): 16.4428,Acc: Train 0.7916, Val 0.3938, Test 0.3950
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 193, Training Time(s): 15.9133, Inference Time(s): 16.5096,Acc: Train 0.7895, Val 0.3930, Test 0.3919
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 194, Training Time(s): 15.8950, Inference Time(s): 16.0605,Acc: Train 0.7902, Val 0.3943, Test 0.3944
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 195, Training Time(s): 17.6677, Inference Time(s): 16.7032,Acc: Train 0.7907, Val 0.3755, Test 0.3777
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 196, Training Time(s): 17.2011, Inference Time(s): 16.0944,Acc: Train 0.7891, Val 0.3850, Test 0.3871
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 197, Training Time(s): 15.1184, Inference Time(s): 16.6714,Acc: Train 0.7890, Val 0.3828, Test 0.3799
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 198, Training Time(s): 16.0319, Inference Time(s): 16.6680,Acc: Train 0.7900, Val 0.3884, Test 0.3868
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 199, Training Time(s): 15.1568, Inference Time(s): 16.3129,Acc: Train 0.7901, Val 0.3840, Test 0.3842
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 200, Training Time(s): 15.5478, Inference Time(s): 16.3438,Acc: Train 0.7919, Val 0.4001, Test 0.3993
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 201, Training Time(s): 14.7392, Inference Time(s): 16.6591,Acc: Train 0.7902, Val 0.3930, Test 0.3930
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 202, Training Time(s): 14.8483, Inference Time(s): 16.5192,Acc: Train 0.7935, Val 0.4004, Test 0.4005
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 203, Training Time(s): 15.3115, Inference Time(s): 17.0115,Acc: Train 0.7916, Val 0.3934, Test 0.3957
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 204, Training Time(s): 15.7994, Inference Time(s): 16.5788,Acc: Train 0.7908, Val 0.3907, Test 0.3932
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 205, Training Time(s): 15.5870, Inference Time(s): 16.6271,Acc: Train 0.7911, Val 0.3926, Test 0.3915
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 206, Training Time(s): 15.3037, Inference Time(s): 16.0934,Acc: Train 0.7902, Val 0.3761, Test 0.3766
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 207, Training Time(s): 15.1235, Inference Time(s): 16.6388,Acc: Train 0.7924, Val 0.3887, Test 0.3890
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 208, Training Time(s): 16.8588, Inference Time(s): 16.8031,Acc: Train 0.7929, Val 0.3952, Test 0.3944
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 209, Training Time(s): 17.0163, Inference Time(s): 16.5772,Acc: Train 0.7920, Val 0.3939, Test 0.3940
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 210, Training Time(s): 16.9961, Inference Time(s): 16.5798,Acc: Train 0.7916, Val 0.3952, Test 0.3956
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 211, Training Time(s): 14.8837, Inference Time(s): 17.0135,Acc: Train 0.7916, Val 0.3973, Test 0.3955
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 212, Training Time(s): 16.0435, Inference Time(s): 16.3510,Acc: Train 0.7900, Val 0.3775, Test 0.3726
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 213, Training Time(s): 15.8735, Inference Time(s): 16.8143,Acc: Train 0.7909, Val 0.3881, Test 0.3870
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 214, Training Time(s): 15.0893, Inference Time(s): 16.6551,Acc: Train 0.7926, Val 0.3836, Test 0.3817
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 215, Training Time(s): 15.1731, Inference Time(s): 16.8596,Acc: Train 0.7922, Val 0.3915, Test 0.3940
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 216, Training Time(s): 16.1393, Inference Time(s): 16.4913,Acc: Train 0.7916, Val 0.3883, Test 0.3878
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 217, Training Time(s): 15.5034, Inference Time(s): 16.5085,Acc: Train 0.7902, Val 0.3829, Test 0.3815
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 218, Training Time(s): 15.5148, Inference Time(s): 16.5869,Acc: Train 0.7911, Val 0.3923, Test 0.3861
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 219, Training Time(s): 14.9257, Inference Time(s): 16.5937,Acc: Train 0.7901, Val 0.3857, Test 0.3869
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 220, Training Time(s): 15.1474, Inference Time(s): 16.3820,Acc: Train 0.7925, Val 0.3917, Test 0.3853
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 221, Training Time(s): 14.8619, Inference Time(s): 16.3998,Acc: Train 0.7917, Val 0.3829, Test 0.3790
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 222, Training Time(s): 15.0374, Inference Time(s): 16.6003,Acc: Train 0.7922, Val 0.3904, Test 0.3841
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 223, Training Time(s): 17.3538, Inference Time(s): 16.5769,Acc: Train 0.7924, Val 0.3708, Test 0.3687
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 224, Training Time(s): 17.2750, Inference Time(s): 16.0549,Acc: Train 0.7938, Val 0.3792, Test 0.3770
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 225, Training Time(s): 17.3963, Inference Time(s): 16.1829,Acc: Train 0.7928, Val 0.3741, Test 0.3708
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 226, Training Time(s): 17.1796, Inference Time(s): 16.0896,Acc: Train 0.7945, Val 0.3979, Test 0.3954
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 227, Training Time(s): 17.2182, Inference Time(s): 16.7161,Acc: Train 0.7919, Val 0.3829, Test 0.3848
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 228, Training Time(s): 17.7126, Inference Time(s): 16.8267,Acc: Train 0.7939, Val 0.3937, Test 0.3868
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 229, Training Time(s): 15.8448, Inference Time(s): 16.3155,Acc: Train 0.7915, Val 0.3942, Test 0.3937
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 230, Training Time(s): 14.7378, Inference Time(s): 16.0461,Acc: Train 0.7920, Val 0.3916, Test 0.3878
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 231, Training Time(s): 17.1760, Inference Time(s): 16.5104,Acc: Train 0.7907, Val 0.3848, Test 0.3786
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 232, Training Time(s): 15.5497, Inference Time(s): 16.3102,Acc: Train 0.7933, Val 0.3955, Test 0.3883
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 233, Training Time(s): 14.9678, Inference Time(s): 16.4872,Acc: Train 0.7943, Val 0.3934, Test 0.3845
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 234, Training Time(s): 14.8650, Inference Time(s): 16.4590,Acc: Train 0.7930, Val 0.3935, Test 0.3833
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 235, Training Time(s): 15.1968, Inference Time(s): 16.6277,Acc: Train 0.7951, Val 0.3942, Test 0.3868
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 236, Training Time(s): 15.1827, Inference Time(s): 16.4836,Acc: Train 0.7940, Val 0.3853, Test 0.3787
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 237, Training Time(s): 15.3905, Inference Time(s): 16.6390,Acc: Train 0.7935, Val 0.3970, Test 0.3881
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 238, Training Time(s): 14.7565, Inference Time(s): 16.6196,Acc: Train 0.7930, Val 0.3917, Test 0.3859
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 239, Training Time(s): 15.2625, Inference Time(s): 16.1564,Acc: Train 0.7915, Val 0.3960, Test 0.3886
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 240, Training Time(s): 14.9797, Inference Time(s): 16.5418,Acc: Train 0.7931, Val 0.3984, Test 0.3968
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 241, Training Time(s): 14.7252, Inference Time(s): 16.4328,Acc: Train 0.7921, Val 0.3939, Test 0.3919
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 242, Training Time(s): 15.0684, Inference Time(s): 16.6222,Acc: Train 0.7919, Val 0.3902, Test 0.3900
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 243, Training Time(s): 15.0093, Inference Time(s): 16.0254,Acc: Train 0.7920, Val 0.3885, Test 0.3884
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 244, Training Time(s): 14.7646, Inference Time(s): 16.3539,Acc: Train 0.7913, Val 0.3761, Test 0.3745
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 245, Training Time(s): 15.3871, Inference Time(s): 16.0633,Acc: Train 0.7931, Val 0.3871, Test 0.3899
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 246, Training Time(s): 14.8950, Inference Time(s): 16.3524,Acc: Train 0.7913, Val 0.3817, Test 0.3803
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 247, Training Time(s): 14.9064, Inference Time(s): 16.2689,Acc: Train 0.7937, Val 0.3994, Test 0.3968
Best Epoch 182, Valid 0.4017, Test 0.4024
Epoch 248, Training Time(s): 14.7726, Inference Time(s): 16.1664,Acc: Train 0.7934, Val 0.4024, Test 0.4044
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 249, Training Time(s): 16.7040, Inference Time(s): 16.0749,Acc: Train 0.7917, Val 0.3991, Test 0.3981
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 250, Training Time(s): 15.0608, Inference Time(s): 16.2037,Acc: Train 0.7933, Val 0.3939, Test 0.3945
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 251, Training Time(s): 17.0222, Inference Time(s): 16.0689,Acc: Train 0.7919, Val 0.3984, Test 0.3961
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 252, Training Time(s): 17.4373, Inference Time(s): 16.5664,Acc: Train 0.7921, Val 0.3940, Test 0.3928
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 253, Training Time(s): 15.1806, Inference Time(s): 16.6201,Acc: Train 0.7920, Val 0.4012, Test 0.3930
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 254, Training Time(s): 15.0226, Inference Time(s): 16.6944,Acc: Train 0.7932, Val 0.3999, Test 0.3960
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 255, Training Time(s): 14.8872, Inference Time(s): 16.7235,Acc: Train 0.7932, Val 0.4013, Test 0.3965
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 256, Training Time(s): 15.3455, Inference Time(s): 15.9871,Acc: Train 0.7935, Val 0.3997, Test 0.3970
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 257, Training Time(s): 15.3498, Inference Time(s): 16.0728,Acc: Train 0.7951, Val 0.3977, Test 0.3962
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 258, Training Time(s): 15.2469, Inference Time(s): 16.3540,Acc: Train 0.7955, Val 0.3911, Test 0.3929
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 259, Training Time(s): 17.5559, Inference Time(s): 16.8069,Acc: Train 0.7952, Val 0.3916, Test 0.3905
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 260, Training Time(s): 14.9410, Inference Time(s): 16.7234,Acc: Train 0.7948, Val 0.3935, Test 0.3962
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 261, Training Time(s): 14.9973, Inference Time(s): 16.3236,Acc: Train 0.7933, Val 0.4001, Test 0.4011
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 262, Training Time(s): 17.0008, Inference Time(s): 16.5053,Acc: Train 0.7916, Val 0.3999, Test 0.3980
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 263, Training Time(s): 17.5101, Inference Time(s): 16.4589,Acc: Train 0.7937, Val 0.3991, Test 0.3979
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 264, Training Time(s): 17.3295, Inference Time(s): 16.1558,Acc: Train 0.7930, Val 0.3967, Test 0.3968
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 265, Training Time(s): 17.5643, Inference Time(s): 16.1628,Acc: Train 0.7929, Val 0.3941, Test 0.3940
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 266, Training Time(s): 15.3637, Inference Time(s): 16.1643,Acc: Train 0.7926, Val 0.3842, Test 0.3851
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 267, Training Time(s): 15.0547, Inference Time(s): 16.9788,Acc: Train 0.7915, Val 0.3923, Test 0.3907
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 268, Training Time(s): 15.3926, Inference Time(s): 16.9648,Acc: Train 0.7928, Val 0.3895, Test 0.3865
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 269, Training Time(s): 15.4822, Inference Time(s): 16.6237,Acc: Train 0.7927, Val 0.4004, Test 0.4004
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 270, Training Time(s): 15.2553, Inference Time(s): 16.7000,Acc: Train 0.7918, Val 0.3932, Test 0.3919
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 271, Training Time(s): 15.2475, Inference Time(s): 16.5062,Acc: Train 0.7929, Val 0.3949, Test 0.3900
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 272, Training Time(s): 15.1036, Inference Time(s): 16.5577,Acc: Train 0.7940, Val 0.3891, Test 0.3865
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 273, Training Time(s): 14.7778, Inference Time(s): 16.4635,Acc: Train 0.7935, Val 0.3952, Test 0.3950
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 274, Training Time(s): 15.0247, Inference Time(s): 16.4723,Acc: Train 0.7923, Val 0.3912, Test 0.3886
Best Epoch 248, Valid 0.4024, Test 0.4044
Epoch 275, Training Time(s): 15.1419, Inference Time(s): 16.7180,Acc: Train 0.7926, Val 0.4040, Test 0.4023
Best Epoch 275, Valid 0.4040, Test 0.4023
Epoch 276, Training Time(s): 14.8133, Inference Time(s): 16.3071,Acc: Train 0.7937, Val 0.4043, Test 0.3997
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 277, Training Time(s): 14.6642, Inference Time(s): 16.6012,Acc: Train 0.7924, Val 0.4038, Test 0.4020
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 278, Training Time(s): 15.1372, Inference Time(s): 16.4904,Acc: Train 0.7937, Val 0.3981, Test 0.3980
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 279, Training Time(s): 15.3814, Inference Time(s): 16.2023,Acc: Train 0.7941, Val 0.4026, Test 0.4000
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 280, Training Time(s): 14.9842, Inference Time(s): 16.5695,Acc: Train 0.7951, Val 0.3979, Test 0.3945
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 281, Training Time(s): 14.6577, Inference Time(s): 16.3106,Acc: Train 0.7945, Val 0.3986, Test 0.3949
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 282, Training Time(s): 14.8075, Inference Time(s): 16.6435,Acc: Train 0.7943, Val 0.3972, Test 0.3936
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 283, Training Time(s): 15.8324, Inference Time(s): 16.5322,Acc: Train 0.7951, Val 0.3930, Test 0.3879
Best Epoch 276, Valid 0.4043, Test 0.3997
Epoch 284, Training Time(s): 17.4862, Inference Time(s): 16.7602,Acc: Train 0.7950, Val 0.4080, Test 0.4028
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 285, Training Time(s): 17.3201, Inference Time(s): 16.1226,Acc: Train 0.7956, Val 0.3992, Test 0.3938
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 286, Training Time(s): 15.0596, Inference Time(s): 16.8286,Acc: Train 0.7946, Val 0.3997, Test 0.3921
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 287, Training Time(s): 15.2870, Inference Time(s): 16.6400,Acc: Train 0.7944, Val 0.4033, Test 0.3965
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 288, Training Time(s): 14.7691, Inference Time(s): 16.3077,Acc: Train 0.7948, Val 0.3957, Test 0.3903
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 289, Training Time(s): 15.3133, Inference Time(s): 15.8782,Acc: Train 0.7936, Val 0.4018, Test 0.4002
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 290, Training Time(s): 14.9598, Inference Time(s): 16.8498,Acc: Train 0.7949, Val 0.3968, Test 0.3910
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 291, Training Time(s): 15.2931, Inference Time(s): 16.3965,Acc: Train 0.7953, Val 0.4046, Test 0.4011
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 292, Training Time(s): 15.7384, Inference Time(s): 16.4495,Acc: Train 0.7925, Val 0.3972, Test 0.3914
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 293, Training Time(s): 15.1183, Inference Time(s): 16.0357,Acc: Train 0.7950, Val 0.3984, Test 0.3956
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 294, Training Time(s): 15.1748, Inference Time(s): 16.0792,Acc: Train 0.7924, Val 0.3899, Test 0.3856
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 295, Training Time(s): 15.6135, Inference Time(s): 15.9855,Acc: Train 0.7955, Val 0.4052, Test 0.4013
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 296, Training Time(s): 15.3242, Inference Time(s): 16.6522,Acc: Train 0.7956, Val 0.4008, Test 0.3920
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 297, Training Time(s): 15.0119, Inference Time(s): 16.5690,Acc: Train 0.7948, Val 0.4004, Test 0.3952
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 298, Training Time(s): 15.1729, Inference Time(s): 16.8184,Acc: Train 0.7963, Val 0.4020, Test 0.3921
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 299, Training Time(s): 14.8250, Inference Time(s): 16.2601,Acc: Train 0.7953, Val 0.3956, Test 0.3868
Best Epoch 284, Valid 0.4080, Test 0.4028
Epoch 300, Training Time(s): 15.4150, Inference Time(s): 16.1754,Acc: Train 0.7953, Val 0.3907, Test 0.3843
Best Epoch 284, Valid 0.4080, Test 0.4028
Best Epoch 284, Valid 0.4080, Test 0.4028
CPU peak RSS: 14.04 GB
[[0.4080056846141815, 0.4027993083000183]]
average, val 0.4080, test 0.4028
all time:9612.4681s
all time:9615.1550s


2. rgcn 使用预训练嵌入
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
loading data costs 6.4326s
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
loading data costs 6.6610s
Convert a graph into a bidirected graph: 8.059 seconds, peak memory: 40.946 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 40.946 GB
[22:54:31] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 42182144 edges into 100 parts and get 9776177 edge cuts
Metis partitioning: 20.439 seconds, peak memory: 42.308 GB
Split the graph: 8.551 seconds
Construct subgraphs: 0.050 seconds
clustering costs 47.1030s
Convert a graph into a bidirected graph: 19.984 seconds, peak memory: 45.496 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 45.496 GB
[22:55:51] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 97322811 edges into 100 parts and get 21708196 edge cuts
Metis partitioning: 38.506 seconds, peak memory: 47.949 GB
Split the graph: 18.131 seconds
Construct subgraphs: 0.048 seconds
clustering costs 89.0295s
Convert a graph into a bidirected graph: 92.922 seconds, peak memory: 67.442 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 67.442 GB
[23:00:59] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 396446017 edges into 100 parts and get 101667923 edge cuts
Metis partitioning: 172.911 seconds, peak memory: 78.186 GB
Split the graph: 74.358 seconds
Construct subgraphs: 0.103 seconds
clustering costs 363.8902s
Convert a graph into a bidirected graph: 7.878 seconds, peak memory: 78.186 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 78.186 GB
[23:03:04] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 42409796 edges into 100 parts and get 9489414 edge cuts
Metis partitioning: 25.870 seconds, peak memory: 78.186 GB
Split the graph: 8.879 seconds
Construct subgraphs: 0.043 seconds
clustering costs 57.4847s
Convert a graph into a bidirected graph: 78.041 seconds, peak memory: 78.186 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 78.186 GB
[23:06:33] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 404098595 edges into 100 parts and get 112038327 edge cuts
Metis partitioning: 99.155 seconds, peak memory: 78.372 GB
Split the graph: 81.403 seconds
Construct subgraphs: 0.086 seconds
clustering costs 281.0924s
Convert a graph into a bidirected graph: 9.110 seconds, peak memory: 78.372 GB
Construct multi-constraint weights: 0.000 seconds, peak memory: 78.372 GB
[23:08:44] /opt/dgl/src/graph/transform/metis_partition_hetero.cc:87: Partition a graph with 1939743 nodes and 51963221 edges into 100 parts and get 14065195 edge cuts
Metis partitioning: 25.262 seconds, peak memory: 78.372 GB
Split the graph: 10.029 seconds
Construct subgraphs: 0.042 seconds
clustering costs 57.3846s
num of nodes in each cluster:19333
100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 100/100 [00:03<00:00, 29.99it/s]
100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 100/100 [00:03<00:00, 30.19it/s]
100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 100/100 [00:03<00:00, 30.31it/s]
100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 100/100 [00:03<00:00, 29.60it/s]
100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 100/100 [00:03<00:00, 30.05it/s]
100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 100/100 [00:03<00:00, 29.88it/s]
num of nodes in each influential nodes:100
allocate time:1132.8414s


使用ComplEx生成嵌入特征：
Processing dataset: ogbn-mag
[ComplEx] Epoch 1/20 - loss: 0.6432                                                                                                                                
[ComplEx] Epoch 2/20 - loss: 0.1842                                                                                                                                
[ComplEx] Epoch 3/20 - loss: 0.0965                                                                                                                                
[ComplEx] Epoch 4/20 - loss: 0.0685                                                                                                                                
[ComplEx] Epoch 5/20 - loss: 0.0566
[ComplEx] Epoch 6/20 - loss: 0.0498                                                                                                                                
[ComplEx] Epoch 7/20 - loss: 0.0455                                                                                                                                
[ComplEx] Epoch 8/20 - loss: 0.0425                                                                                                                                
[ComplEx] Epoch 9/20 - loss: 0.0403                                                                                                                                
[ComplEx] Epoch 10/20 - loss: 0.0385                                                                                                                               
[ComplEx] Epoch 11/20 - loss: 0.0372                                                                                                                               
[ComplEx] Epoch 12/20 - loss: 0.0361                                                                                                                               
[ComplEx] Epoch 13/20 - loss: 0.0351                                                                                                                               
[ComplEx] Epoch 14/20 - loss: 0.0344                                                                                                                               
[ComplEx] Epoch 15/20 - loss: 0.0337                                                                                                                               
[ComplEx] Epoch 16/20 - loss: 0.0332                                                                                                                               
[ComplEx] Epoch 17/20 - loss: 0.0327                                                                                                                               
[ComplEx] Epoch 18/20 - loss: 0.0322                                                                                                                               
[ComplEx] Epoch 19/20 - loss: 0.0319                                                                                                                               
[ComplEx] Epoch 20/20 - loss: 0.0315                                                                                                                               
Epoch: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 20/20 [2:08:09<00:00, 384.47s/it]
Saved author.pt with shape (1134649, 128) -> ../dataset/ogbn_mag/author.pt
Saved field_of_study.pt with shape (59965, 128) -> ../dataset/ogbn_mag/field_of_study.pt
Saved institution.pt with shape (8740, 128) -> ../dataset/ogbn_mag/institution.pt
Saved paper.pt with shape (736389, 128) -> ../dataset/ogbn_mag/paper.pt
==================================================
Finished processing dataset: ogbn-mag
==================================================

All datasets processed successfully!



(pytorch) zw@node18:~/code/test/SubInfer$ torchrun --standalone --nproc_per_node=2 scripts/classify_Ogbn.py --model rgcn --use_emb
WARNING:torch.distributed.run:
*****************************************
Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
*****************************************
Namespace(local_rank=0, device=0, num_layer=3, hidden_channel=512, dropout=0.5, lr=0.001, epochs=300, batch_size=25000, runs=1, save_path='../partition', dataset='ogbn-mag', num_parts=100, mask=0.5, supply_rate=0.1, use_emb=True, model='rgcn', all_models=False)
Namespace(local_rank=1, device=0, num_layer=3, hidden_channel=512, dropout=0.5, lr=0.001, epochs=300, batch_size=25000, runs=1, save_path='../partition', dataset='ogbn-mag', num_parts=100, mask=0.5, supply_rate=0.1, use_emb=True, model='rgcn', all_models=False)
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
loading data costs 5.6424s
# Nodes: 1939743
# Edges: 42222014
# Train: 629571
# Val: 64879
# Test: 41939
# Classes: 349
Found existing partition file at ../partition/mag-rgcn-partition100.pkl, skip computing.
loading data costs 5.8632s
tensor([     16,      86,     121,  ..., 1850692,  200892, 1150356])
tensor([     16,      86,     121,  ..., 1850692,  200892, 1150356])
training
RGCN(
  (input_dropout): Dropout(p=0.0, inplace=False)
  (dropout): Dropout(p=0.5, inplace=False)
  (rgat): ModuleList(
    (0): RGCN_layer()
    (1): RGCN_layer()
    (2): RGCN_layer()
  )
  (mlp): Sequential(
    (0): Linear(in_features=512, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.5, inplace=False)
    (3): Linear(in_features=512, out_features=512, bias=True)
    (4): ReLU()
    (5): Dropout(p=0.5, inplace=False)
    (6): Linear(in_features=512, out_features=349, bias=True)
  )
  (linear): Linear(in_features=128, out_features=512, bias=True)
  (label_linear): Linear(in_features=349, out_features=512, bias=True)
  (norms): ModuleList(
    (0): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (inception_ffs): ModuleList(
    (0): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (1): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (2): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
  )
)
training
RGCN(
  (input_dropout): Dropout(p=0.0, inplace=False)
  (dropout): Dropout(p=0.5, inplace=False)
  (rgat): ModuleList(
    (0): RGCN_layer()
    (1): RGCN_layer()
    (2): RGCN_layer()
  )
  (mlp): Sequential(
    (0): Linear(in_features=512, out_features=512, bias=True)
    (1): ReLU()
    (2): Dropout(p=0.5, inplace=False)
    (3): Linear(in_features=512, out_features=512, bias=True)
    (4): ReLU()
    (5): Dropout(p=0.5, inplace=False)
    (6): Linear(in_features=512, out_features=349, bias=True)
  )
  (linear): Linear(in_features=128, out_features=512, bias=True)
  (label_linear): Linear(in_features=349, out_features=512, bias=True)
  (norms): ModuleList(
    (0): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (1): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): BatchNorm1d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  )
  (inception_ffs): ModuleList(
    (0): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (1): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
    (2): FeedForwardNet(
      (layers): ModuleList(
        (0): Linear(in_features=512, out_features=512, bias=True)
      )
    )
  )
)
# Params: 1740637
# Params: 1740637
Using device: cuda:0
Using device: cuda:1
[W reducer.cpp:1251] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())
[W reducer.cpp:1251] Warning: find_unused_parameters=True was specified in DDP constructor, but did not find any unused parameters in the forward pass. This flag results in an extra traversal of the autograd graph every iteration,  which can adversely affect performance. If your model indeed never has any unused parameters in the forward pass, consider turning this flag off. Note that this warning may be a false positive if your model has flow control causing later iterations to have unused parameters. (function operator())
Epoch 1, Training Time(s): 16.3387, Inference Time(s): 16.6804,Acc: Train 0.2026, Val 0.1679, Test 0.1831
[Mem] GPU MiB (train_peak_alloc/resv: 2.2/4.5, infer_peak_alloc/resv: 7.1/14.8),
CPU MiB (train: 12.1, infer: 12.9)
CPU MiB delta (train: 0.7, infer: 1.2)
Best Epoch 1, Valid 0.1679, Test 0.1831
Epoch 2, Training Time(s): 14.5640, Inference Time(s): 15.8172,Acc: Train 0.3734, Val 0.2634, Test 0.2723
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 2, Valid 0.2634, Test 0.2723
Epoch 3, Training Time(s): 14.5571, Inference Time(s): 15.5381,Acc: Train 0.5540, Val 0.2784, Test 0.2826
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 3, Valid 0.2784, Test 0.2826
Epoch 4, Training Time(s): 14.4942, Inference Time(s): 15.6678,Acc: Train 0.6693, Val 0.2929, Test 0.2997
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 4, Valid 0.2929, Test 0.2997
Epoch 5, Training Time(s): 14.6422, Inference Time(s): 16.3498,Acc: Train 0.7254, Val 0.3212, Test 0.3204
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 5, Valid 0.3212, Test 0.3204
Epoch 6, Training Time(s): 15.2865, Inference Time(s): 16.1569,Acc: Train 0.7538, Val 0.3420, Test 0.3367
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 6, Valid 0.3420, Test 0.3367
Epoch 7, Training Time(s): 14.8612, Inference Time(s): 15.9180,Acc: Train 0.7624, Val 0.3406, Test 0.3316
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 6, Valid 0.3420, Test 0.3367
Epoch 8, Training Time(s): 15.6625, Inference Time(s): 15.9764,Acc: Train 0.7698, Val 0.3493, Test 0.3418
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 8, Valid 0.3493, Test 0.3418
Epoch 9, Training Time(s): 14.4298, Inference Time(s): 19.3636,Acc: Train 0.7741, Val 0.3517, Test 0.3463
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 9, Valid 0.3517, Test 0.3463
Epoch 10, Training Time(s): 15.8495, Inference Time(s): 16.5069,Acc: Train 0.7782, Val 0.3597, Test 0.3551
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 10, Valid 0.3597, Test 0.3551
Epoch 11, Training Time(s): 15.3603, Inference Time(s): 15.8056,Acc: Train 0.7813, Val 0.3603, Test 0.3547
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 11, Valid 0.3603, Test 0.3547
Epoch 12, Training Time(s): 15.0821, Inference Time(s): 15.8140,Acc: Train 0.7838, Val 0.3678, Test 0.3584
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 12, Valid 0.3678, Test 0.3584
Epoch 13, Training Time(s): 14.7603, Inference Time(s): 16.4323,Acc: Train 0.7861, Val 0.3725, Test 0.3628
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 13, Valid 0.3725, Test 0.3628
Epoch 14, Training Time(s): 14.7668, Inference Time(s): 16.3511,Acc: Train 0.7857, Val 0.3779, Test 0.3699
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 14, Valid 0.3779, Test 0.3699
Epoch 15, Training Time(s): 14.7389, Inference Time(s): 15.9621,Acc: Train 0.7894, Val 0.3844, Test 0.3751
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 15, Valid 0.3844, Test 0.3751
Epoch 16, Training Time(s): 14.9978, Inference Time(s): 16.3345,Acc: Train 0.7907, Val 0.3924, Test 0.3838
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 16, Valid 0.3924, Test 0.3838
Epoch 17, Training Time(s): 15.0973, Inference Time(s): 16.3410,Acc: Train 0.7929, Val 0.4018, Test 0.3971
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 17, Valid 0.4018, Test 0.3971
Epoch 18, Training Time(s): 15.2102, Inference Time(s): 16.3777,Acc: Train 0.7915, Val 0.4039, Test 0.3950
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 18, Valid 0.4039, Test 0.3950
Epoch 19, Training Time(s): 14.9465, Inference Time(s): 16.3858,Acc: Train 0.7953, Val 0.4058, Test 0.3970
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 19, Valid 0.4058, Test 0.3970
Epoch 20, Training Time(s): 14.6871, Inference Time(s): 16.0710,Acc: Train 0.7989, Val 0.4078, Test 0.3978
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 20, Valid 0.4078, Test 0.3978
Epoch 21, Training Time(s): 14.8909, Inference Time(s): 15.9794,Acc: Train 0.8001, Val 0.4083, Test 0.3972
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 22, Training Time(s): 14.9533, Inference Time(s): 16.1234,Acc: Train 0.7992, Val 0.4033, Test 0.3930
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 23, Training Time(s): 14.9176, Inference Time(s): 16.2520,Acc: Train 0.7991, Val 0.3992, Test 0.3838
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 24, Training Time(s): 14.9901, Inference Time(s): 16.2984,Acc: Train 0.7993, Val 0.3932, Test 0.3785
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 25, Training Time(s): 15.6383, Inference Time(s): 15.9176,Acc: Train 0.8000, Val 0.4052, Test 0.3907
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 26, Training Time(s): 14.7379, Inference Time(s): 16.1500,Acc: Train 0.7954, Val 0.4024, Test 0.3904
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 27, Training Time(s): 14.7494, Inference Time(s): 15.9655,Acc: Train 0.7923, Val 0.4007, Test 0.3879
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 28, Training Time(s): 14.6474, Inference Time(s): 15.7914,Acc: Train 0.8024, Val 0.4005, Test 0.3871
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 29, Training Time(s): 14.7217, Inference Time(s): 18.1775,Acc: Train 0.8041, Val 0.4050, Test 0.3940
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 30, Training Time(s): 14.8568, Inference Time(s): 16.0321,Acc: Train 0.7994, Val 0.3940, Test 0.3832
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 31, Training Time(s): 15.5999, Inference Time(s): 16.2433,Acc: Train 0.7989, Val 0.3908, Test 0.3808
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 32, Training Time(s): 15.2260, Inference Time(s): 16.1066,Acc: Train 0.8014, Val 0.3963, Test 0.3848
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 33, Training Time(s): 14.8431, Inference Time(s): 16.1170,Acc: Train 0.8042, Val 0.4026, Test 0.3928
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 21, Valid 0.4083, Test 0.3972
Epoch 34, Training Time(s): 15.0564, Inference Time(s): 16.3617,Acc: Train 0.8051, Val 0.4112, Test 0.3971
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 34, Valid 0.4112, Test 0.3971
Epoch 35, Training Time(s): 14.7573, Inference Time(s): 16.2030,Acc: Train 0.8067, Val 0.4126, Test 0.3999
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 35, Valid 0.4126, Test 0.3999
Epoch 36, Training Time(s): 14.8755, Inference Time(s): 16.1949,Acc: Train 0.8090, Val 0.4172, Test 0.4022
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 36, Valid 0.4172, Test 0.4022
Epoch 37, Training Time(s): 14.8262, Inference Time(s): 16.2821,Acc: Train 0.8112, Val 0.4243, Test 0.4110
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 37, Valid 0.4243, Test 0.4110
Epoch 38, Training Time(s): 14.8237, Inference Time(s): 16.2553,Acc: Train 0.8075, Val 0.4101, Test 0.3936
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 37, Valid 0.4243, Test 0.4110
Epoch 39, Training Time(s): 14.9687, Inference Time(s): 16.3687,Acc: Train 0.8063, Val 0.3988, Test 0.3794
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 37, Valid 0.4243, Test 0.4110
Epoch 40, Training Time(s): 15.0485, Inference Time(s): 16.6253,Acc: Train 0.8077, Val 0.3990, Test 0.3822
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 37, Valid 0.4243, Test 0.4110
Epoch 41, Training Time(s): 14.6824, Inference Time(s): 16.2088,Acc: Train 0.8103, Val 0.4039, Test 0.3871
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 37, Valid 0.4243, Test 0.4110
Epoch 42, Training Time(s): 14.6100, Inference Time(s): 16.1075,Acc: Train 0.8130, Val 0.4165, Test 0.4006
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 37, Valid 0.4243, Test 0.4110
Epoch 43, Training Time(s): 14.6834, Inference Time(s): 15.9692,Acc: Train 0.8150, Val 0.4214, Test 0.4054
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 37, Valid 0.4243, Test 0.4110
Epoch 44, Training Time(s): 14.6739, Inference Time(s): 16.3551,Acc: Train 0.8142, Val 0.4294, Test 0.4157
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 45, Training Time(s): 15.1159, Inference Time(s): 16.1768,Acc: Train 0.8143, Val 0.4220, Test 0.4074
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 46, Training Time(s): 14.7124, Inference Time(s): 15.9792,Acc: Train 0.8113, Val 0.4171, Test 0.4025
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 47, Training Time(s): 14.5595, Inference Time(s): 15.9077,Acc: Train 0.8123, Val 0.4170, Test 0.3993
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 48, Training Time(s): 14.5242, Inference Time(s): 16.1139,Acc: Train 0.8132, Val 0.4183, Test 0.3998
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 49, Training Time(s): 14.7666, Inference Time(s): 16.3135,Acc: Train 0.8149, Val 0.4160, Test 0.3938
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 50, Training Time(s): 15.5021, Inference Time(s): 16.0294,Acc: Train 0.8154, Val 0.4213, Test 0.4032
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 51, Training Time(s): 14.5734, Inference Time(s): 15.9556,Acc: Train 0.8148, Val 0.4117, Test 0.3924
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 52, Training Time(s): 14.6715, Inference Time(s): 16.4511,Acc: Train 0.8139, Val 0.4226, Test 0.4022
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 53, Training Time(s): 14.9777, Inference Time(s): 16.3687,Acc: Train 0.8165, Val 0.4195, Test 0.4018
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 54, Training Time(s): 14.9300, Inference Time(s): 16.1805,Acc: Train 0.8151, Val 0.4176, Test 0.3976
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 55, Training Time(s): 14.6262, Inference Time(s): 16.2904,Acc: Train 0.8158, Val 0.4113, Test 0.3913
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 56, Training Time(s): 15.1046, Inference Time(s): 18.7173,Acc: Train 0.8183, Val 0.4226, Test 0.4001
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 44, Valid 0.4294, Test 0.4157
Epoch 57, Training Time(s): 15.2891, Inference Time(s): 16.5908,Acc: Train 0.8195, Val 0.4367, Test 0.4220
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 57, Valid 0.4367, Test 0.4220
Epoch 58, Training Time(s): 14.6442, Inference Time(s): 16.2933,Acc: Train 0.8155, Val 0.4363, Test 0.4198
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 57, Valid 0.4367, Test 0.4220
Epoch 59, Training Time(s): 14.9075, Inference Time(s): 15.8845,Acc: Train 0.8106, Val 0.4275, Test 0.4146
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 57, Valid 0.4367, Test 0.4220
Epoch 60, Training Time(s): 14.9336, Inference Time(s): 16.1147,Acc: Train 0.8201, Val 0.4368, Test 0.4165
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 60, Valid 0.4368, Test 0.4165
Epoch 61, Training Time(s): 15.2648, Inference Time(s): 16.3715,Acc: Train 0.8201, Val 0.4311, Test 0.4079
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 60, Valid 0.4368, Test 0.4165
Epoch 62, Training Time(s): 14.7708, Inference Time(s): 16.3276,Acc: Train 0.8184, Val 0.4242, Test 0.4012
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 60, Valid 0.4368, Test 0.4165
Epoch 63, Training Time(s): 15.6733, Inference Time(s): 18.0840,Acc: Train 0.8182, Val 0.4158, Test 0.3911
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 60, Valid 0.4368, Test 0.4165
Epoch 64, Training Time(s): 15.1373, Inference Time(s): 16.0424,Acc: Train 0.8214, Val 0.4366, Test 0.4144
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 60, Valid 0.4368, Test 0.4165
Epoch 65, Training Time(s): 14.8842, Inference Time(s): 18.6135,Acc: Train 0.8205, Val 0.4345, Test 0.4158
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 60, Valid 0.4368, Test 0.4165
Epoch 66, Training Time(s): 14.9449, Inference Time(s): 16.0563,Acc: Train 0.8228, Val 0.4416, Test 0.4225
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 66, Valid 0.4416, Test 0.4225
Epoch 67, Training Time(s): 15.0296, Inference Time(s): 16.4365,Acc: Train 0.8247, Val 0.4405, Test 0.4216
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 66, Valid 0.4416, Test 0.4225
Epoch 68, Training Time(s): 14.7869, Inference Time(s): 16.5201,Acc: Train 0.8248, Val 0.4446, Test 0.4269
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 68, Valid 0.4446, Test 0.4269
Epoch 69, Training Time(s): 14.5340, Inference Time(s): 16.5030,Acc: Train 0.8255, Val 0.4461, Test 0.4306
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 69, Valid 0.4461, Test 0.4306
Epoch 70, Training Time(s): 14.7578, Inference Time(s): 16.4373,Acc: Train 0.8259, Val 0.4510, Test 0.4320
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 71, Training Time(s): 14.6241, Inference Time(s): 16.5772,Acc: Train 0.8253, Val 0.4451, Test 0.4311
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 72, Training Time(s): 14.6530, Inference Time(s): 16.3002,Acc: Train 0.8209, Val 0.4334, Test 0.4174
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 73, Training Time(s): 15.0975, Inference Time(s): 16.3815,Acc: Train 0.8198, Val 0.4374, Test 0.4227
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 74, Training Time(s): 15.3212, Inference Time(s): 16.7783,Acc: Train 0.8187, Val 0.4323, Test 0.4153
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 75, Training Time(s): 15.5830, Inference Time(s): 18.6379,Acc: Train 0.8241, Val 0.4416, Test 0.4218
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 76, Training Time(s): 14.8979, Inference Time(s): 16.0093,Acc: Train 0.8240, Val 0.4430, Test 0.4206
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 77, Training Time(s): 15.2371, Inference Time(s): 16.3149,Acc: Train 0.8242, Val 0.4392, Test 0.4145
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 78, Training Time(s): 14.6934, Inference Time(s): 16.4184,Acc: Train 0.8236, Val 0.4349, Test 0.4084
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 79, Training Time(s): 14.4255, Inference Time(s): 16.0580,Acc: Train 0.8252, Val 0.4353, Test 0.4097
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 70, Valid 0.4510, Test 0.4320
Epoch 80, Training Time(s): 14.7765, Inference Time(s): 16.1029,Acc: Train 0.8292, Val 0.4530, Test 0.4321
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 81, Training Time(s): 15.3691, Inference Time(s): 16.2755,Acc: Train 0.8297, Val 0.4507, Test 0.4317
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 82, Training Time(s): 15.6499, Inference Time(s): 16.3929,Acc: Train 0.8290, Val 0.4514, Test 0.4309
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 83, Training Time(s): 14.8477, Inference Time(s): 17.1025,Acc: Train 0.8264, Val 0.4388, Test 0.4170
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 84, Training Time(s): 15.8248, Inference Time(s): 16.5319,Acc: Train 0.8284, Val 0.4480, Test 0.4254
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 85, Training Time(s): 15.2353, Inference Time(s): 16.2642,Acc: Train 0.8291, Val 0.4446, Test 0.4210
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 86, Training Time(s): 15.0996, Inference Time(s): 16.6894,Acc: Train 0.8275, Val 0.4413, Test 0.4161
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 87, Training Time(s): 15.4844, Inference Time(s): 16.6873,Acc: Train 0.8269, Val 0.4429, Test 0.4201
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 88, Training Time(s): 14.7477, Inference Time(s): 16.7682,Acc: Train 0.8277, Val 0.4378, Test 0.4160
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 89, Training Time(s): 15.2745, Inference Time(s): 15.9959,Acc: Train 0.8286, Val 0.4440, Test 0.4229
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 90, Training Time(s): 14.7521, Inference Time(s): 16.3299,Acc: Train 0.8317, Val 0.4505, Test 0.4305
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 91, Training Time(s): 14.6063, Inference Time(s): 18.6015,Acc: Train 0.8319, Val 0.4515, Test 0.4307
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 92, Training Time(s): 15.1255, Inference Time(s): 16.0024,Acc: Train 0.8261, Val 0.4416, Test 0.4216
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 93, Training Time(s): 15.1750, Inference Time(s): 16.2969,Acc: Train 0.8234, Val 0.4366, Test 0.4208
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 94, Training Time(s): 15.1767, Inference Time(s): 16.3072,Acc: Train 0.8279, Val 0.4419, Test 0.4240
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 95, Training Time(s): 14.9968, Inference Time(s): 16.1357,Acc: Train 0.8314, Val 0.4499, Test 0.4302
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 96, Training Time(s): 15.3718, Inference Time(s): 16.2164,Acc: Train 0.8295, Val 0.4371, Test 0.4196
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 97, Training Time(s): 14.8258, Inference Time(s): 16.3924,Acc: Train 0.8305, Val 0.4515, Test 0.4345
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 80, Valid 0.4530, Test 0.4321
Epoch 98, Training Time(s): 14.9139, Inference Time(s): 17.0066,Acc: Train 0.8317, Val 0.4580, Test 0.4398
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 98, Valid 0.4580, Test 0.4398
Epoch 99, Training Time(s): 15.7303, Inference Time(s): 16.4018,Acc: Train 0.8309, Val 0.4619, Test 0.4444
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 100, Training Time(s): 15.1786, Inference Time(s): 16.0997,Acc: Train 0.8278, Val 0.4506, Test 0.4341
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 101, Training Time(s): 14.9120, Inference Time(s): 16.1622,Acc: Train 0.8294, Val 0.4520, Test 0.4327
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 102, Training Time(s): 15.0999, Inference Time(s): 16.6621,Acc: Train 0.8312, Val 0.4602, Test 0.4415
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 103, Training Time(s): 14.6587, Inference Time(s): 16.5834,Acc: Train 0.8329, Val 0.4550, Test 0.4348
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 104, Training Time(s): 15.0489, Inference Time(s): 18.0263,Acc: Train 0.8345, Val 0.4545, Test 0.4359
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 105, Training Time(s): 15.0140, Inference Time(s): 18.8963,Acc: Train 0.8342, Val 0.4590, Test 0.4379
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 106, Training Time(s): 14.9404, Inference Time(s): 16.4212,Acc: Train 0.8306, Val 0.4493, Test 0.4294
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 107, Training Time(s): 15.6282, Inference Time(s): 16.2852,Acc: Train 0.8314, Val 0.4530, Test 0.4311
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 108, Training Time(s): 14.6050, Inference Time(s): 16.2407,Acc: Train 0.8332, Val 0.4617, Test 0.4384
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 109, Training Time(s): 14.9202, Inference Time(s): 16.3896,Acc: Train 0.8323, Val 0.4510, Test 0.4251
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 110, Training Time(s): 15.2575, Inference Time(s): 16.3959,Acc: Train 0.8308, Val 0.4404, Test 0.4115
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 111, Training Time(s): 14.4389, Inference Time(s): 16.5913,Acc: Train 0.8310, Val 0.4380, Test 0.4113
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 112, Training Time(s): 15.6549, Inference Time(s): 16.1978,Acc: Train 0.8312, Val 0.4356, Test 0.4069
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 113, Training Time(s): 14.9367, Inference Time(s): 16.4348,Acc: Train 0.8320, Val 0.4425, Test 0.4147
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 114, Training Time(s): 15.4410, Inference Time(s): 16.3348,Acc: Train 0.8314, Val 0.4319, Test 0.4062
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 115, Training Time(s): 15.6528, Inference Time(s): 16.1942,Acc: Train 0.8329, Val 0.4450, Test 0.4218
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 116, Training Time(s): 14.7927, Inference Time(s): 16.3681,Acc: Train 0.8360, Val 0.4618, Test 0.4429
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 117, Training Time(s): 14.9749, Inference Time(s): 16.5079,Acc: Train 0.8319, Val 0.4518, Test 0.4327
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 118, Training Time(s): 14.8047, Inference Time(s): 16.3710,Acc: Train 0.8300, Val 0.4530, Test 0.4357
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 99, Valid 0.4619, Test 0.4444
Epoch 119, Training Time(s): 15.1147, Inference Time(s): 16.5400,Acc: Train 0.8344, Val 0.4621, Test 0.4433
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 119, Valid 0.4621, Test 0.4433
Epoch 120, Training Time(s): 14.8999, Inference Time(s): 16.8930,Acc: Train 0.8357, Val 0.4626, Test 0.4389
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 120, Valid 0.4626, Test 0.4389
Epoch 121, Training Time(s): 15.6037, Inference Time(s): 16.3734,Acc: Train 0.8338, Val 0.4576, Test 0.4354
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 120, Valid 0.4626, Test 0.4389
Epoch 122, Training Time(s): 14.8715, Inference Time(s): 16.3541,Acc: Train 0.8342, Val 0.4615, Test 0.4407
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 120, Valid 0.4626, Test 0.4389
Epoch 123, Training Time(s): 14.9140, Inference Time(s): 16.2903,Acc: Train 0.8333, Val 0.4556, Test 0.4369
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 120, Valid 0.4626, Test 0.4389
Epoch 124, Training Time(s): 15.5707, Inference Time(s): 16.2510,Acc: Train 0.8352, Val 0.4675, Test 0.4447
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 124, Valid 0.4675, Test 0.4447
Epoch 125, Training Time(s): 15.2100, Inference Time(s): 16.0548,Acc: Train 0.8371, Val 0.4682, Test 0.4450
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 125, Valid 0.4682, Test 0.4450
Epoch 126, Training Time(s): 14.9032, Inference Time(s): 16.5119,Acc: Train 0.8359, Val 0.4687, Test 0.4457
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 127, Training Time(s): 14.7562, Inference Time(s): 16.4276,Acc: Train 0.8332, Val 0.4554, Test 0.4336
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 128, Training Time(s): 15.0950, Inference Time(s): 16.2505,Acc: Train 0.8335, Val 0.4496, Test 0.4263
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 129, Training Time(s): 14.7832, Inference Time(s): 16.1068,Acc: Train 0.8357, Val 0.4573, Test 0.4324
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 130, Training Time(s): 14.9253, Inference Time(s): 16.2823,Acc: Train 0.8349, Val 0.4474, Test 0.4197
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 131, Training Time(s): 15.8428, Inference Time(s): 16.3696,Acc: Train 0.8352, Val 0.4498, Test 0.4222
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 132, Training Time(s): 15.6136, Inference Time(s): 16.8444,Acc: Train 0.8337, Val 0.4433, Test 0.4141
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 133, Training Time(s): 14.7959, Inference Time(s): 16.3226,Acc: Train 0.8312, Val 0.4368, Test 0.4068
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 134, Training Time(s): 15.1606, Inference Time(s): 16.2360,Acc: Train 0.8314, Val 0.4353, Test 0.4032
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 135, Training Time(s): 14.8496, Inference Time(s): 16.4767,Acc: Train 0.8351, Val 0.4409, Test 0.4110
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 136, Training Time(s): 14.8934, Inference Time(s): 16.5593,Acc: Train 0.8362, Val 0.4549, Test 0.4283
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 137, Training Time(s): 15.0446, Inference Time(s): 16.3706,Acc: Train 0.8366, Val 0.4558, Test 0.4306
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 138, Training Time(s): 14.6985, Inference Time(s): 16.1770,Acc: Train 0.8373, Val 0.4514, Test 0.4281
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 139, Training Time(s): 14.9124, Inference Time(s): 16.1223,Acc: Train 0.8383, Val 0.4551, Test 0.4330
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 140, Training Time(s): 14.8664, Inference Time(s): 16.4401,Acc: Train 0.8360, Val 0.4538, Test 0.4340
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 141, Training Time(s): 14.9608, Inference Time(s): 16.6840,Acc: Train 0.8342, Val 0.4536, Test 0.4329
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 142, Training Time(s): 14.9840, Inference Time(s): 16.1308,Acc: Train 0.8360, Val 0.4539, Test 0.4304
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 143, Training Time(s): 14.7147, Inference Time(s): 18.5704,Acc: Train 0.8374, Val 0.4608, Test 0.4372
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 144, Training Time(s): 15.3166, Inference Time(s): 16.6194,Acc: Train 0.8393, Val 0.4617, Test 0.4368
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 145, Training Time(s): 15.9710, Inference Time(s): 19.2880,Acc: Train 0.8386, Val 0.4668, Test 0.4424
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 146, Training Time(s): 15.2300, Inference Time(s): 16.5984,Acc: Train 0.8383, Val 0.4599, Test 0.4350
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 147, Training Time(s): 14.6714, Inference Time(s): 16.1931,Acc: Train 0.8350, Val 0.4575, Test 0.4314
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 148, Training Time(s): 14.7047, Inference Time(s): 16.3625,Acc: Train 0.8320, Val 0.4509, Test 0.4271
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 149, Training Time(s): 15.0493, Inference Time(s): 16.6080,Acc: Train 0.8364, Val 0.4571, Test 0.4343
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 150, Training Time(s): 14.7980, Inference Time(s): 16.1006,Acc: Train 0.8365, Val 0.4532, Test 0.4296
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 151, Training Time(s): 14.5852, Inference Time(s): 16.5533,Acc: Train 0.8347, Val 0.4492, Test 0.4265
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 152, Training Time(s): 14.6289, Inference Time(s): 16.4556,Acc: Train 0.8342, Val 0.4344, Test 0.4113
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 153, Training Time(s): 14.9622, Inference Time(s): 16.6081,Acc: Train 0.8334, Val 0.4386, Test 0.4170
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 154, Training Time(s): 15.3916, Inference Time(s): 16.5485,Acc: Train 0.8336, Val 0.4455, Test 0.4196
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 155, Training Time(s): 15.5310, Inference Time(s): 16.2805,Acc: Train 0.8359, Val 0.4583, Test 0.4404
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 156, Training Time(s): 14.7011, Inference Time(s): 16.2882,Acc: Train 0.8389, Val 0.4622, Test 0.4426
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 157, Training Time(s): 15.0477, Inference Time(s): 16.2037,Acc: Train 0.8387, Val 0.4596, Test 0.4429
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 158, Training Time(s): 15.0226, Inference Time(s): 16.3202,Acc: Train 0.8406, Val 0.4592, Test 0.4379
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 159, Training Time(s): 14.8835, Inference Time(s): 16.0601,Acc: Train 0.8384, Val 0.4645, Test 0.4471
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 160, Training Time(s): 14.8956, Inference Time(s): 16.4667,Acc: Train 0.8378, Val 0.4499, Test 0.4343
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 161, Training Time(s): 14.5588, Inference Time(s): 16.3612,Acc: Train 0.8365, Val 0.4598, Test 0.4429
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 162, Training Time(s): 14.6947, Inference Time(s): 16.2966,Acc: Train 0.8376, Val 0.4569, Test 0.4394
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 163, Training Time(s): 14.7591, Inference Time(s): 16.2975,Acc: Train 0.8373, Val 0.4558, Test 0.4379
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 164, Training Time(s): 14.7308, Inference Time(s): 16.4343,Acc: Train 0.8359, Val 0.4527, Test 0.4341
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 165, Training Time(s): 14.7957, Inference Time(s): 16.3042,Acc: Train 0.8361, Val 0.4520, Test 0.4342
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 166, Training Time(s): 14.8556, Inference Time(s): 16.3620,Acc: Train 0.8399, Val 0.4537, Test 0.4375
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 167, Training Time(s): 14.9157, Inference Time(s): 16.3402,Acc: Train 0.8417, Val 0.4605, Test 0.4422
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 168, Training Time(s): 14.9367, Inference Time(s): 16.5620,Acc: Train 0.8406, Val 0.4660, Test 0.4445
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 169, Training Time(s): 15.0675, Inference Time(s): 16.5534,Acc: Train 0.8401, Val 0.4601, Test 0.4438
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 170, Training Time(s): 14.6427, Inference Time(s): 16.1350,Acc: Train 0.8392, Val 0.4538, Test 0.4340
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 171, Training Time(s): 14.5298, Inference Time(s): 15.9537,Acc: Train 0.8360, Val 0.4434, Test 0.4218
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 172, Training Time(s): 14.5896, Inference Time(s): 16.3920,Acc: Train 0.8375, Val 0.4337, Test 0.4099
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 173, Training Time(s): 14.6536, Inference Time(s): 16.3274,Acc: Train 0.8376, Val 0.4447, Test 0.4223
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 174, Training Time(s): 15.0202, Inference Time(s): 16.3575,Acc: Train 0.8396, Val 0.4380, Test 0.4194
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 175, Training Time(s): 15.3651, Inference Time(s): 16.3721,Acc: Train 0.8407, Val 0.4439, Test 0.4243
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 176, Training Time(s): 15.4825, Inference Time(s): 16.9671,Acc: Train 0.8415, Val 0.4468, Test 0.4245
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 177, Training Time(s): 14.7558, Inference Time(s): 16.3505,Acc: Train 0.8408, Val 0.4441, Test 0.4251
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 178, Training Time(s): 14.7925, Inference Time(s): 16.0136,Acc: Train 0.8388, Val 0.4487, Test 0.4333
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 179, Training Time(s): 14.8412, Inference Time(s): 16.4045,Acc: Train 0.8395, Val 0.4525, Test 0.4342
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 180, Training Time(s): 15.5796, Inference Time(s): 16.3871,Acc: Train 0.8408, Val 0.4573, Test 0.4348
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 181, Training Time(s): 14.9152, Inference Time(s): 16.3303,Acc: Train 0.8417, Val 0.4604, Test 0.4412
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 182, Training Time(s): 14.9315, Inference Time(s): 16.3730,Acc: Train 0.8417, Val 0.4544, Test 0.4351
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 183, Training Time(s): 15.0750, Inference Time(s): 16.5549,Acc: Train 0.8382, Val 0.4535, Test 0.4371
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 184, Training Time(s): 15.3655, Inference Time(s): 16.3160,Acc: Train 0.8414, Val 0.4541, Test 0.4369
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 185, Training Time(s): 15.0115, Inference Time(s): 16.0765,Acc: Train 0.8418, Val 0.4527, Test 0.4297
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 186, Training Time(s): 15.1859, Inference Time(s): 16.4466,Acc: Train 0.8373, Val 0.4447, Test 0.4211
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 187, Training Time(s): 14.7507, Inference Time(s): 16.6627,Acc: Train 0.8408, Val 0.4503, Test 0.4266
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 188, Training Time(s): 15.1664, Inference Time(s): 16.2717,Acc: Train 0.8411, Val 0.4477, Test 0.4246
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 189, Training Time(s): 14.9337, Inference Time(s): 16.5352,Acc: Train 0.8410, Val 0.4418, Test 0.4178
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 190, Training Time(s): 15.1081, Inference Time(s): 16.3652,Acc: Train 0.8387, Val 0.4348, Test 0.4114
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 191, Training Time(s): 15.1222, Inference Time(s): 16.5129,Acc: Train 0.8394, Val 0.4381, Test 0.4112
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 192, Training Time(s): 14.9355, Inference Time(s): 16.9669,Acc: Train 0.8403, Val 0.4383, Test 0.4113
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 193, Training Time(s): 15.3289, Inference Time(s): 16.1886,Acc: Train 0.8427, Val 0.4540, Test 0.4301
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 194, Training Time(s): 14.9225, Inference Time(s): 16.2976,Acc: Train 0.8424, Val 0.4577, Test 0.4353
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 195, Training Time(s): 14.8184, Inference Time(s): 16.3051,Acc: Train 0.8429, Val 0.4548, Test 0.4328
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 196, Training Time(s): 14.8591, Inference Time(s): 16.5274,Acc: Train 0.8446, Val 0.4619, Test 0.4380
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 197, Training Time(s): 15.1634, Inference Time(s): 18.4838,Acc: Train 0.8449, Val 0.4624, Test 0.4396
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 198, Training Time(s): 15.0153, Inference Time(s): 16.3842,Acc: Train 0.8415, Val 0.4538, Test 0.4341
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 199, Training Time(s): 14.8635, Inference Time(s): 16.3238,Acc: Train 0.8397, Val 0.4481, Test 0.4291
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 200, Training Time(s): 14.6188, Inference Time(s): 16.9070,Acc: Train 0.8431, Val 0.4534, Test 0.4343
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 201, Training Time(s): 15.7180, Inference Time(s): 19.0394,Acc: Train 0.8435, Val 0.4527, Test 0.4286
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 202, Training Time(s): 15.8740, Inference Time(s): 18.9936,Acc: Train 0.8431, Val 0.4554, Test 0.4300
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 203, Training Time(s): 14.9902, Inference Time(s): 16.6163,Acc: Train 0.8410, Val 0.4538, Test 0.4273
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 204, Training Time(s): 15.0649, Inference Time(s): 16.3436,Acc: Train 0.8406, Val 0.4437, Test 0.4185
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 205, Training Time(s): 14.9005, Inference Time(s): 16.2333,Acc: Train 0.8411, Val 0.4435, Test 0.4168
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 206, Training Time(s): 14.5707, Inference Time(s): 16.3004,Acc: Train 0.8421, Val 0.4485, Test 0.4255
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 207, Training Time(s): 14.6102, Inference Time(s): 16.4923,Acc: Train 0.8453, Val 0.4531, Test 0.4299
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 208, Training Time(s): 14.8573, Inference Time(s): 16.5830,Acc: Train 0.8410, Val 0.4495, Test 0.4293
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 209, Training Time(s): 15.7003, Inference Time(s): 16.7124,Acc: Train 0.8447, Val 0.4580, Test 0.4367
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 210, Training Time(s): 15.1088, Inference Time(s): 16.3576,Acc: Train 0.8446, Val 0.4567, Test 0.4337
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 211, Training Time(s): 14.7713, Inference Time(s): 16.4258,Acc: Train 0.8453, Val 0.4619, Test 0.4396
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 212, Training Time(s): 15.0851, Inference Time(s): 16.1761,Acc: Train 0.8443, Val 0.4613, Test 0.4360
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 213, Training Time(s): 15.2210, Inference Time(s): 17.1091,Acc: Train 0.8447, Val 0.4649, Test 0.4430
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 214, Training Time(s): 15.6557, Inference Time(s): 16.4333,Acc: Train 0.8451, Val 0.4599, Test 0.4380
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 215, Training Time(s): 15.1228, Inference Time(s): 16.2634,Acc: Train 0.8461, Val 0.4629, Test 0.4413
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 216, Training Time(s): 14.4788, Inference Time(s): 16.1653,Acc: Train 0.8436, Val 0.4556, Test 0.4340
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 217, Training Time(s): 14.9388, Inference Time(s): 16.3620,Acc: Train 0.8414, Val 0.4401, Test 0.4127
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 218, Training Time(s): 15.3685, Inference Time(s): 16.3854,Acc: Train 0.8384, Val 0.4324, Test 0.4023
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 219, Training Time(s): 14.8708, Inference Time(s): 16.5251,Acc: Train 0.8427, Val 0.4466, Test 0.4181
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 220, Training Time(s): 15.4418, Inference Time(s): 16.8074,Acc: Train 0.8447, Val 0.4586, Test 0.4365
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 221, Training Time(s): 15.4057, Inference Time(s): 16.7749,Acc: Train 0.8456, Val 0.4552, Test 0.4289
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 222, Training Time(s): 15.0607, Inference Time(s): 16.3769,Acc: Train 0.8454, Val 0.4600, Test 0.4382
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 223, Training Time(s): 15.0647, Inference Time(s): 16.4729,Acc: Train 0.8450, Val 0.4599, Test 0.4365
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 224, Training Time(s): 15.2838, Inference Time(s): 16.6363,Acc: Train 0.8435, Val 0.4544, Test 0.4344
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 225, Training Time(s): 15.0300, Inference Time(s): 16.7940,Acc: Train 0.8469, Val 0.4612, Test 0.4412
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 226, Training Time(s): 14.8226, Inference Time(s): 16.3930,Acc: Train 0.8437, Val 0.4530, Test 0.4284
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 227, Training Time(s): 15.0076, Inference Time(s): 18.9019,Acc: Train 0.8441, Val 0.4468, Test 0.4191
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 228, Training Time(s): 15.6359, Inference Time(s): 16.4828,Acc: Train 0.8447, Val 0.4487, Test 0.4235
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 229, Training Time(s): 14.8707, Inference Time(s): 16.5592,Acc: Train 0.8444, Val 0.4498, Test 0.4231
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 230, Training Time(s): 15.0389, Inference Time(s): 16.6206,Acc: Train 0.8438, Val 0.4445, Test 0.4190
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 231, Training Time(s): 15.7688, Inference Time(s): 16.4211,Acc: Train 0.8426, Val 0.4380, Test 0.4132
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 232, Training Time(s): 15.0391, Inference Time(s): 18.6876,Acc: Train 0.8449, Val 0.4442, Test 0.4201
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 233, Training Time(s): 14.6927, Inference Time(s): 16.5586,Acc: Train 0.8433, Val 0.4434, Test 0.4189
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 234, Training Time(s): 15.0982, Inference Time(s): 16.5957,Acc: Train 0.8437, Val 0.4519, Test 0.4259
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 235, Training Time(s): 14.7414, Inference Time(s): 16.5300,Acc: Train 0.8463, Val 0.4591, Test 0.4374
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 236, Training Time(s): 14.6098, Inference Time(s): 16.3310,Acc: Train 0.8462, Val 0.4670, Test 0.4458
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 237, Training Time(s): 14.8659, Inference Time(s): 19.2882,Acc: Train 0.8465, Val 0.4589, Test 0.4347
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 238, Training Time(s): 15.9235, Inference Time(s): 16.3086,Acc: Train 0.8446, Val 0.4566, Test 0.4303
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 239, Training Time(s): 15.2204, Inference Time(s): 16.4173,Acc: Train 0.8447, Val 0.4525, Test 0.4271
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 240, Training Time(s): 14.8156, Inference Time(s): 16.1455,Acc: Train 0.8470, Val 0.4547, Test 0.4329
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 241, Training Time(s): 15.4043, Inference Time(s): 16.4621,Acc: Train 0.8472, Val 0.4522, Test 0.4302
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 242, Training Time(s): 14.6883, Inference Time(s): 16.7332,Acc: Train 0.8476, Val 0.4571, Test 0.4333
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 243, Training Time(s): 15.1580, Inference Time(s): 16.7677,Acc: Train 0.8441, Val 0.4548, Test 0.4293
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 244, Training Time(s): 15.9464, Inference Time(s): 16.5498,Acc: Train 0.8426, Val 0.4487, Test 0.4234
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 245, Training Time(s): 15.0711, Inference Time(s): 16.6575,Acc: Train 0.8428, Val 0.4531, Test 0.4287
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 246, Training Time(s): 14.6934, Inference Time(s): 16.3808,Acc: Train 0.8439, Val 0.4518, Test 0.4272
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 247, Training Time(s): 14.6288, Inference Time(s): 16.1295,Acc: Train 0.8424, Val 0.4402, Test 0.4153
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 248, Training Time(s): 14.7597, Inference Time(s): 16.3890,Acc: Train 0.8425, Val 0.4403, Test 0.4139
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 249, Training Time(s): 14.7331, Inference Time(s): 16.5269,Acc: Train 0.8458, Val 0.4417, Test 0.4201
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 250, Training Time(s): 14.7330, Inference Time(s): 16.3439,Acc: Train 0.8451, Val 0.4479, Test 0.4288
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 251, Training Time(s): 14.9177, Inference Time(s): 16.4921,Acc: Train 0.8472, Val 0.4537, Test 0.4332
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 252, Training Time(s): 14.7006, Inference Time(s): 16.4721,Acc: Train 0.8452, Val 0.4573, Test 0.4337
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 253, Training Time(s): 14.9685, Inference Time(s): 16.4876,Acc: Train 0.8446, Val 0.4508, Test 0.4326
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 254, Training Time(s): 15.1941, Inference Time(s): 16.3405,Acc: Train 0.8438, Val 0.4471, Test 0.4274
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 255, Training Time(s): 15.2138, Inference Time(s): 16.3889,Acc: Train 0.8464, Val 0.4558, Test 0.4351
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 256, Training Time(s): 15.1053, Inference Time(s): 16.4362,Acc: Train 0.8472, Val 0.4636, Test 0.4426
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 257, Training Time(s): 14.8129, Inference Time(s): 16.3736,Acc: Train 0.8472, Val 0.4638, Test 0.4434
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 258, Training Time(s): 15.1226, Inference Time(s): 16.6672,Acc: Train 0.8453, Val 0.4543, Test 0.4313
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 259, Training Time(s): 14.9840, Inference Time(s): 16.2151,Acc: Train 0.8465, Val 0.4511, Test 0.4262
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 260, Training Time(s): 15.0279, Inference Time(s): 16.1831,Acc: Train 0.8441, Val 0.4466, Test 0.4210
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 261, Training Time(s): 15.1204, Inference Time(s): 18.8829,Acc: Train 0.8435, Val 0.4379, Test 0.4093
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 262, Training Time(s): 14.9264, Inference Time(s): 16.5387,Acc: Train 0.8448, Val 0.4394, Test 0.4168
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 263, Training Time(s): 15.3617, Inference Time(s): 16.4372,Acc: Train 0.8465, Val 0.4409, Test 0.4209
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 264, Training Time(s): 14.9020, Inference Time(s): 16.4789,Acc: Train 0.8481, Val 0.4559, Test 0.4334
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 265, Training Time(s): 15.0690, Inference Time(s): 16.4285,Acc: Train 0.8474, Val 0.4528, Test 0.4297
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 266, Training Time(s): 14.8699, Inference Time(s): 16.4063,Acc: Train 0.8452, Val 0.4499, Test 0.4278
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 267, Training Time(s): 15.1422, Inference Time(s): 16.9238,Acc: Train 0.8466, Val 0.4497, Test 0.4263
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 268, Training Time(s): 15.4386, Inference Time(s): 17.0259,Acc: Train 0.8477, Val 0.4556, Test 0.4327
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 269, Training Time(s): 15.6008, Inference Time(s): 16.7111,Acc: Train 0.8485, Val 0.4541, Test 0.4290
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 270, Training Time(s): 15.4523, Inference Time(s): 16.6684,Acc: Train 0.8498, Val 0.4597, Test 0.4382
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 271, Training Time(s): 14.5576, Inference Time(s): 16.7012,Acc: Train 0.8469, Val 0.4480, Test 0.4263
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 272, Training Time(s): 15.2167, Inference Time(s): 16.3547,Acc: Train 0.8435, Val 0.4484, Test 0.4290
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 273, Training Time(s): 15.2174, Inference Time(s): 16.6013,Acc: Train 0.8458, Val 0.4537, Test 0.4294
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 274, Training Time(s): 14.9454, Inference Time(s): 16.4021,Acc: Train 0.8476, Val 0.4536, Test 0.4308
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 275, Training Time(s): 15.0680, Inference Time(s): 16.1881,Acc: Train 0.8493, Val 0.4536, Test 0.4298
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 276, Training Time(s): 15.5050, Inference Time(s): 16.0425,Acc: Train 0.8489, Val 0.4611, Test 0.4397
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 277, Training Time(s): 14.6870, Inference Time(s): 16.3976,Acc: Train 0.8470, Val 0.4529, Test 0.4252
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 278, Training Time(s): 14.5677, Inference Time(s): 16.5219,Acc: Train 0.8467, Val 0.4471, Test 0.4195
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 279, Training Time(s): 14.8417, Inference Time(s): 16.5930,Acc: Train 0.8474, Val 0.4438, Test 0.4152
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 280, Training Time(s): 14.8316, Inference Time(s): 16.3972,Acc: Train 0.8483, Val 0.4429, Test 0.4156
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 281, Training Time(s): 15.0948, Inference Time(s): 16.4117,Acc: Train 0.8487, Val 0.4444, Test 0.4163
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 282, Training Time(s): 15.1574, Inference Time(s): 16.6848,Acc: Train 0.8489, Val 0.4543, Test 0.4266
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 283, Training Time(s): 14.9244, Inference Time(s): 16.3932,Acc: Train 0.8454, Val 0.4550, Test 0.4284
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 284, Training Time(s): 15.0213, Inference Time(s): 16.2973,Acc: Train 0.8477, Val 0.4656, Test 0.4377
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 285, Training Time(s): 14.7575, Inference Time(s): 16.4125,Acc: Train 0.8493, Val 0.4586, Test 0.4334
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 286, Training Time(s): 15.2500, Inference Time(s): 16.2753,Acc: Train 0.8492, Val 0.4590, Test 0.4337
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 287, Training Time(s): 14.9710, Inference Time(s): 16.4534,Acc: Train 0.8507, Val 0.4666, Test 0.4419
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 288, Training Time(s): 14.9534, Inference Time(s): 16.0251,Acc: Train 0.8491, Val 0.4608, Test 0.4349
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 289, Training Time(s): 14.6018, Inference Time(s): 15.9854,Acc: Train 0.8478, Val 0.4593, Test 0.4367
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 290, Training Time(s): 14.8630, Inference Time(s): 16.3059,Acc: Train 0.8458, Val 0.4484, Test 0.4217
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 291, Training Time(s): 14.7234, Inference Time(s): 16.6104,Acc: Train 0.8449, Val 0.4419, Test 0.4090
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 292, Training Time(s): 14.8957, Inference Time(s): 16.4559,Acc: Train 0.8425, Val 0.4313, Test 0.4029
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 293, Training Time(s): 14.8326, Inference Time(s): 16.3442,Acc: Train 0.8463, Val 0.4438, Test 0.4179
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 294, Training Time(s): 15.0708, Inference Time(s): 16.6907,Acc: Train 0.8484, Val 0.4451, Test 0.4198
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 295, Training Time(s): 14.7279, Inference Time(s): 16.6030,Acc: Train 0.8501, Val 0.4576, Test 0.4332
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 296, Training Time(s): 14.9736, Inference Time(s): 16.4093,Acc: Train 0.8494, Val 0.4560, Test 0.4294
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 297, Training Time(s): 14.7709, Inference Time(s): 16.5741,Acc: Train 0.8486, Val 0.4538, Test 0.4279
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 298, Training Time(s): 14.8695, Inference Time(s): 16.3214,Acc: Train 0.8484, Val 0.4437, Test 0.4177
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Epoch 299, Training Time(s): 15.0022, Inference Time(s): 16.3902,Acc: Train 0.8481, Val 0.4472, Test 0.4195
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
all time:9483.0898s
Epoch 300, Training Time(s): 15.1979, Inference Time(s): 16.6958,Acc: Train 0.8464, Val 0.4513, Test 0.4220
[Mem] GPU MiB (train_peak_alloc/resv: 3.2/13.3, infer_peak_alloc/resv: 7.1/13.3),
CPU MiB (train: 14.0, infer: 14.0)
CPU MiB delta (train: -0.0, infer: 0.0)
Best Epoch 126, Valid 0.4687, Test 0.4457
Best Epoch 126, Valid 0.4687, Test 0.4457
[[0.4687495231628418, 0.44571879506111145]]
average, val 0.4687, test 0.4457
all time:9483.3604s


二、PubMed数据集
1. nars
(pytorch) zw@node18:~/code/test/SubInfer/scripts$ torchrun --nproc_per_node=2 link_PubMed.py --model nars
WARNING:torch.distributed.run:
*****************************************
Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
*****************************************
Namespace(device=0, num_layer=3, hidden_channel=256, dropout=0.1, lr=0.001, epochs=300, batch_size=1000, runs=1, save_path='../partition', model='nars', local_rank=1, num_parts=100)
Namespace(device=0, num_layer=3, hidden_channel=256, dropout=0.1, lr=0.001, epochs=300, batch_size=1000, runs=1, save_path='../partition', model='nars', local_rank=0, num_parts=100)
# Nodes: 63109
# Edges: 359717
# Nodes: 63109
# Edges: 359717
Loading data took 107.5764s
Found existing partition file at ../partition/PubMed-nars-partition100.pkl, skip computing.
Model: nars
Params: 513536
Using device: cuda:0
Model: nars
Params: 513536
Using device: cuda:1
Propagation Mem (CPU GiB: 97.6 -> 97.8, GPU GiB peak: 0.2)Propagation Mem (CPU GiB: 97.6 -> 97.8, GPU GiB peak: 0.2)

Best Epoch 1, AUC 0.4705, AP 0.4792
Epoch 1, Loss: 0.6975, AUC: 0.4705, AP: 0.4792, Training Time(s): 0.0613, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Epoch 2, Loss: 0.6934, AUC: 0.4649, AP: 0.4772, Training Time(s): 0.0422, Inference Time(s): 0.0163, Propagation Time(s): 1.3676
Epoch 3, Loss: 0.6905, AUC: 0.4678, AP: 0.4790, Training Time(s): 0.0421, Inference Time(s): 0.0151, Propagation Time(s): 1.3676
Best Epoch 4, AUC 0.4729, AP 0.4823
Epoch 4, Loss: 0.6885, AUC: 0.4729, AP: 0.4823, Training Time(s): 0.0422, Inference Time(s): 0.0196, Propagation Time(s): 1.3676
Best Epoch 5, AUC 0.4775, AP 0.4840
Epoch 5, Loss: 0.6869, AUC: 0.4775, AP: 0.4840, Training Time(s): 0.0421, Inference Time(s): 0.0201, Propagation Time(s): 1.3676
Best Epoch 6, AUC 0.4780, AP 0.4848
Epoch 6, Loss: 0.6855, AUC: 0.4780, AP: 0.4848, Training Time(s): 0.0389, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Best Epoch 7, AUC 0.4785, AP 0.4851
Epoch 7, Loss: 0.6847, AUC: 0.4785, AP: 0.4851, Training Time(s): 0.0419, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Best Epoch 8, AUC 0.4796, AP 0.4862
Epoch 8, Loss: 0.6840, AUC: 0.4796, AP: 0.4862, Training Time(s): 0.0391, Inference Time(s): 0.0178, Propagation Time(s): 1.3676
Best Epoch 9, AUC 0.4839, AP 0.4881
Epoch 9, Loss: 0.6839, AUC: 0.4839, AP: 0.4881, Training Time(s): 0.0391, Inference Time(s): 0.0185, Propagation Time(s): 1.3676
Best Epoch 10, AUC 0.4894, AP 0.4906
Epoch 10, Loss: 0.6840, AUC: 0.4894, AP: 0.4906, Training Time(s): 0.0410, Inference Time(s): 0.0183, Propagation Time(s): 1.3676
Best Epoch 11, AUC 0.4957, AP 0.4938
Epoch 11, Loss: 0.6843, AUC: 0.4957, AP: 0.4938, Training Time(s): 0.0387, Inference Time(s): 0.0174, Propagation Time(s): 1.3676
Best Epoch 12, AUC 0.5015, AP 0.4972
Epoch 12, Loss: 0.6845, AUC: 0.5015, AP: 0.4972, Training Time(s): 0.0387, Inference Time(s): 0.0207, Propagation Time(s): 1.3676
Best Epoch 13, AUC 0.5074, AP 0.5012
Epoch 13, Loss: 0.6846, AUC: 0.5074, AP: 0.5012, Training Time(s): 0.0388, Inference Time(s): 0.0198, Propagation Time(s): 1.3676
Best Epoch 14, AUC 0.5145, AP 0.5062
Epoch 14, Loss: 0.6846, AUC: 0.5145, AP: 0.5062, Training Time(s): 0.0390, Inference Time(s): 0.0199, Propagation Time(s): 1.3676
Best Epoch 15, AUC 0.5212, AP 0.5119
Epoch 15, Loss: 0.6845, AUC: 0.5212, AP: 0.5119, Training Time(s): 0.0385, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Best Epoch 16, AUC 0.5279, AP 0.5184
Epoch 16, Loss: 0.6842, AUC: 0.5279, AP: 0.5184, Training Time(s): 0.0387, Inference Time(s): 0.0194, Propagation Time(s): 1.3676
Best Epoch 17, AUC 0.5344, AP 0.5262
Epoch 17, Loss: 0.6839, AUC: 0.5344, AP: 0.5262, Training Time(s): 0.0413, Inference Time(s): 0.0183, Propagation Time(s): 1.3676
Best Epoch 18, AUC 0.5389, AP 0.5331
Epoch 18, Loss: 0.6836, AUC: 0.5389, AP: 0.5331, Training Time(s): 0.0444, Inference Time(s): 0.0199, Propagation Time(s): 1.3676
Best Epoch 19, AUC 0.5412, AP 0.5378
Epoch 19, Loss: 0.6835, AUC: 0.5412, AP: 0.5378, Training Time(s): 0.0389, Inference Time(s): 0.0214, Propagation Time(s): 1.3676
Best Epoch 20, AUC 0.5427, AP 0.5409
Epoch 20, Loss: 0.6834, AUC: 0.5427, AP: 0.5409, Training Time(s): 0.0385, Inference Time(s): 0.0191, Propagation Time(s): 1.3676
Best Epoch 21, AUC 0.5434, AP 0.5431
Epoch 21, Loss: 0.6833, AUC: 0.5434, AP: 0.5431, Training Time(s): 0.0391, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 22, AUC 0.5436, AP 0.5446
Epoch 22, Loss: 0.6833, AUC: 0.5436, AP: 0.5446, Training Time(s): 0.0407, Inference Time(s): 0.0173, Propagation Time(s): 1.3676
Epoch 23, Loss: 0.6834, AUC: 0.5436, AP: 0.5446, Training Time(s): 0.0389, Inference Time(s): 0.0172, Propagation Time(s): 1.3676
Best Epoch 24, AUC 0.5437, AP 0.5454
Epoch 24, Loss: 0.6833, AUC: 0.5437, AP: 0.5454, Training Time(s): 0.0383, Inference Time(s): 0.0192, Propagation Time(s): 1.3676
Best Epoch 25, AUC 0.5439, AP 0.5461
Epoch 25, Loss: 0.6833, AUC: 0.5439, AP: 0.5461, Training Time(s): 0.0394, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Best Epoch 26, AUC 0.5442, AP 0.5468
Epoch 26, Loss: 0.6832, AUC: 0.5442, AP: 0.5468, Training Time(s): 0.0383, Inference Time(s): 0.0195, Propagation Time(s): 1.3676
Best Epoch 27, AUC 0.5444, AP 0.5473
Epoch 27, Loss: 0.6831, AUC: 0.5444, AP: 0.5473, Training Time(s): 0.0409, Inference Time(s): 0.0175, Propagation Time(s): 1.3676
Best Epoch 28, AUC 0.5447, AP 0.5479
Epoch 28, Loss: 0.6830, AUC: 0.5447, AP: 0.5479, Training Time(s): 0.0382, Inference Time(s): 0.0160, Propagation Time(s): 1.3676
Best Epoch 29, AUC 0.5450, AP 0.5482
Epoch 29, Loss: 0.6829, AUC: 0.5450, AP: 0.5482, Training Time(s): 0.0392, Inference Time(s): 0.0173, Propagation Time(s): 1.3676
Best Epoch 30, AUC 0.5452, AP 0.5485
Epoch 30, Loss: 0.6826, AUC: 0.5452, AP: 0.5485, Training Time(s): 0.0387, Inference Time(s): 0.0165, Propagation Time(s): 1.3676
Best Epoch 31, AUC 0.5454, AP 0.5489
Epoch 31, Loss: 0.6827, AUC: 0.5454, AP: 0.5489, Training Time(s): 0.0384, Inference Time(s): 0.0183, Propagation Time(s): 1.3676
Best Epoch 32, AUC 0.5455, AP 0.5492
Epoch 32, Loss: 0.6826, AUC: 0.5455, AP: 0.5492, Training Time(s): 0.0410, Inference Time(s): 0.0186, Propagation Time(s): 1.3676
Best Epoch 33, AUC 0.5456, AP 0.5494
Epoch 33, Loss: 0.6826, AUC: 0.5456, AP: 0.5494, Training Time(s): 0.0382, Inference Time(s): 0.0147, Propagation Time(s): 1.3676
Best Epoch 34, AUC 0.5456, AP 0.5497
Epoch 34, Loss: 0.6825, AUC: 0.5456, AP: 0.5497, Training Time(s): 0.0388, Inference Time(s): 0.0209, Propagation Time(s): 1.3676
Best Epoch 35, AUC 0.5457, AP 0.5499
Epoch 35, Loss: 0.6824, AUC: 0.5457, AP: 0.5499, Training Time(s): 0.0383, Inference Time(s): 0.0144, Propagation Time(s): 1.3676
Epoch 36, Loss: 0.6823, AUC: 0.5457, AP: 0.5500, Training Time(s): 0.0389, Inference Time(s): 0.0170, Propagation Time(s): 1.3676
Epoch 37, Loss: 0.6822, AUC: 0.5456, AP: 0.5501, Training Time(s): 0.0405, Inference Time(s): 0.0175, Propagation Time(s): 1.3676
Epoch 38, Loss: 0.6821, AUC: 0.5456, AP: 0.5502, Training Time(s): 0.0392, Inference Time(s): 0.0198, Propagation Time(s): 1.3676
Epoch 39, Loss: 0.6820, AUC: 0.5455, AP: 0.5502, Training Time(s): 0.0388, Inference Time(s): 0.0204, Propagation Time(s): 1.3676
Epoch 40, Loss: 0.6820, AUC: 0.5454, AP: 0.5502, Training Time(s): 0.0383, Inference Time(s): 0.0186, Propagation Time(s): 1.3676
Epoch 41, Loss: 0.6818, AUC: 0.5454, AP: 0.5503, Training Time(s): 0.0394, Inference Time(s): 0.0179, Propagation Time(s): 1.3676
Epoch 42, Loss: 0.6819, AUC: 0.5453, AP: 0.5504, Training Time(s): 0.0408, Inference Time(s): 0.0147, Propagation Time(s): 1.3676
Epoch 43, Loss: 0.6816, AUC: 0.5453, AP: 0.5505, Training Time(s): 0.0385, Inference Time(s): 0.0148, Propagation Time(s): 1.3676
Epoch 44, Loss: 0.6815, AUC: 0.5453, AP: 0.5506, Training Time(s): 0.0387, Inference Time(s): 0.0223, Propagation Time(s): 1.3676
Epoch 45, Loss: 0.6814, AUC: 0.5452, AP: 0.5507, Training Time(s): 0.0383, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Epoch 46, Loss: 0.6813, AUC: 0.5453, AP: 0.5509, Training Time(s): 0.0392, Inference Time(s): 0.0176, Propagation Time(s): 1.3676
Epoch 47, Loss: 0.6812, AUC: 0.5453, AP: 0.5510, Training Time(s): 0.0405, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Epoch 48, Loss: 0.6810, AUC: 0.5453, AP: 0.5512, Training Time(s): 0.0383, Inference Time(s): 0.0161, Propagation Time(s): 1.3676
Epoch 49, Loss: 0.6810, AUC: 0.5453, AP: 0.5513, Training Time(s): 0.0431, Inference Time(s): 0.0156, Propagation Time(s): 1.3676
Epoch 50, Loss: 0.6808, AUC: 0.5453, AP: 0.5514, Training Time(s): 0.0407, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Epoch 51, Loss: 0.6808, AUC: 0.5453, AP: 0.5515, Training Time(s): 0.0387, Inference Time(s): 0.0145, Propagation Time(s): 1.3676
Epoch 52, Loss: 0.6806, AUC: 0.5453, AP: 0.5516, Training Time(s): 0.0397, Inference Time(s): 0.0213, Propagation Time(s): 1.3676
Epoch 53, Loss: 0.6806, AUC: 0.5453, AP: 0.5517, Training Time(s): 0.0385, Inference Time(s): 0.0155, Propagation Time(s): 1.3676
Epoch 54, Loss: 0.6803, AUC: 0.5453, AP: 0.5518, Training Time(s): 0.0398, Inference Time(s): 0.0179, Propagation Time(s): 1.3676
Best Epoch 55, AUC 0.5458, AP 0.5546
Epoch 55, Loss: 0.6802, AUC: 0.5458, AP: 0.5546, Training Time(s): 0.0396, Inference Time(s): 0.0196, Propagation Time(s): 1.3676
Best Epoch 56, AUC 0.5459, AP 0.5572
Epoch 56, Loss: 0.6800, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0415, Inference Time(s): 0.0206, Propagation Time(s): 1.3676
Epoch 57, Loss: 0.6798, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0407, Inference Time(s): 0.0203, Propagation Time(s): 1.3676
Epoch 58, Loss: 0.6797, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0463, Inference Time(s): 0.0203, Propagation Time(s): 1.3676
Epoch 59, Loss: 0.6795, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0434, Inference Time(s): 0.0154, Propagation Time(s): 1.3676
Epoch 60, Loss: 0.6794, AUC: 0.5458, AP: 0.5572, Training Time(s): 0.0490, Inference Time(s): 0.0170, Propagation Time(s): 1.3676
Epoch 61, Loss: 0.6792, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0396, Inference Time(s): 0.0171, Propagation Time(s): 1.3676
Epoch 62, Loss: 0.6790, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0403, Inference Time(s): 0.0212, Propagation Time(s): 1.3676
Epoch 63, Loss: 0.6789, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0399, Inference Time(s): 0.0148, Propagation Time(s): 1.3676
Epoch 64, Loss: 0.6787, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0477, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Epoch 65, Loss: 0.6785, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0430, Inference Time(s): 0.0194, Propagation Time(s): 1.3676
Epoch 66, Loss: 0.6783, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0405, Inference Time(s): 0.0183, Propagation Time(s): 1.3676
Best Epoch 67, AUC 0.5459, AP 0.5572
Epoch 67, Loss: 0.6780, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0454, Inference Time(s): 0.0173, Propagation Time(s): 1.3676
Best Epoch 68, AUC 0.5459, AP 0.5572
Epoch 68, Loss: 0.6778, AUC: 0.5459, AP: 0.5572, Training Time(s): 0.0475, Inference Time(s): 0.0187, Propagation Time(s): 1.3676
Best Epoch 69, AUC 0.5459, AP 0.5571
Epoch 69, Loss: 0.6776, AUC: 0.5459, AP: 0.5571, Training Time(s): 0.0397, Inference Time(s): 0.0177, Propagation Time(s): 1.3676
Best Epoch 70, AUC 0.5459, AP 0.5571
Epoch 70, Loss: 0.6774, AUC: 0.5459, AP: 0.5571, Training Time(s): 0.0415, Inference Time(s): 0.0183, Propagation Time(s): 1.3676
Best Epoch 71, AUC 0.5460, AP 0.5571
Epoch 71, Loss: 0.6773, AUC: 0.5460, AP: 0.5571, Training Time(s): 0.0403, Inference Time(s): 0.0177, Propagation Time(s): 1.3676
Best Epoch 72, AUC 0.5460, AP 0.5571
Epoch 72, Loss: 0.6769, AUC: 0.5460, AP: 0.5571, Training Time(s): 0.0410, Inference Time(s): 0.0186, Propagation Time(s): 1.3676
Best Epoch 73, AUC 0.5460, AP 0.5571
Epoch 73, Loss: 0.6767, AUC: 0.5460, AP: 0.5571, Training Time(s): 0.0391, Inference Time(s): 0.0179, Propagation Time(s): 1.3676
Best Epoch 74, AUC 0.5460, AP 0.5571
Epoch 74, Loss: 0.6765, AUC: 0.5460, AP: 0.5571, Training Time(s): 0.0387, Inference Time(s): 0.0174, Propagation Time(s): 1.3676
Best Epoch 75, AUC 0.5460, AP 0.5571
Epoch 75, Loss: 0.6762, AUC: 0.5460, AP: 0.5571, Training Time(s): 0.0414, Inference Time(s): 0.0184, Propagation Time(s): 1.3676
Best Epoch 76, AUC 0.5462, AP 0.5571
Epoch 76, Loss: 0.6761, AUC: 0.5462, AP: 0.5571, Training Time(s): 0.0389, Inference Time(s): 0.0195, Propagation Time(s): 1.3676
Epoch 77, Loss: 0.6758, AUC: 0.5461, AP: 0.5552, Training Time(s): 0.0407, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Epoch 78, Loss: 0.6756, AUC: 0.5458, AP: 0.5544, Training Time(s): 0.0388, Inference Time(s): 0.0191, Propagation Time(s): 1.3676
Epoch 79, Loss: 0.6753, AUC: 0.5458, AP: 0.5544, Training Time(s): 0.0391, Inference Time(s): 0.0260, Propagation Time(s): 1.3676
Epoch 80, Loss: 0.6749, AUC: 0.5459, AP: 0.5544, Training Time(s): 0.0469, Inference Time(s): 0.0200, Propagation Time(s): 1.3676
Epoch 81, Loss: 0.6747, AUC: 0.5459, AP: 0.5544, Training Time(s): 0.0464, Inference Time(s): 0.0237, Propagation Time(s): 1.3676
Epoch 82, Loss: 0.6744, AUC: 0.5460, AP: 0.5544, Training Time(s): 0.0462, Inference Time(s): 0.0207, Propagation Time(s): 1.3676
Epoch 83, Loss: 0.6740, AUC: 0.5460, AP: 0.5545, Training Time(s): 0.0462, Inference Time(s): 0.0247, Propagation Time(s): 1.3676
Epoch 84, Loss: 0.6736, AUC: 0.5460, AP: 0.5545, Training Time(s): 0.0466, Inference Time(s): 0.0223, Propagation Time(s): 1.3676
Epoch 85, Loss: 0.6735, AUC: 0.5461, AP: 0.5545, Training Time(s): 0.0484, Inference Time(s): 0.0205, Propagation Time(s): 1.3676
Epoch 86, Loss: 0.6733, AUC: 0.5461, AP: 0.5546, Training Time(s): 0.0464, Inference Time(s): 0.0223, Propagation Time(s): 1.3676
Best Epoch 87, AUC 0.5462, AP 0.5546
Epoch 87, Loss: 0.6730, AUC: 0.5462, AP: 0.5546, Training Time(s): 0.0471, Inference Time(s): 0.0235, Propagation Time(s): 1.3676
Best Epoch 88, AUC 0.5462, AP 0.5547
Epoch 88, Loss: 0.6727, AUC: 0.5462, AP: 0.5547, Training Time(s): 0.0465, Inference Time(s): 0.0200, Propagation Time(s): 1.3676
Best Epoch 89, AUC 0.5463, AP 0.5547
Epoch 89, Loss: 0.6721, AUC: 0.5463, AP: 0.5547, Training Time(s): 0.0461, Inference Time(s): 0.0220, Propagation Time(s): 1.3676
Best Epoch 90, AUC 0.5463, AP 0.5548
Epoch 90, Loss: 0.6720, AUC: 0.5463, AP: 0.5548, Training Time(s): 0.0452, Inference Time(s): 0.0213, Propagation Time(s): 1.3676
Best Epoch 91, AUC 0.5464, AP 0.5548
Epoch 91, Loss: 0.6717, AUC: 0.5464, AP: 0.5548, Training Time(s): 0.0466, Inference Time(s): 0.0240, Propagation Time(s): 1.3676
Best Epoch 92, AUC 0.5465, AP 0.5549
Epoch 92, Loss: 0.6713, AUC: 0.5465, AP: 0.5549, Training Time(s): 0.0462, Inference Time(s): 0.0211, Propagation Time(s): 1.3676
Best Epoch 93, AUC 0.5465, AP 0.5549
Epoch 93, Loss: 0.6710, AUC: 0.5465, AP: 0.5549, Training Time(s): 0.0455, Inference Time(s): 0.0219, Propagation Time(s): 1.3676
Best Epoch 94, AUC 0.5466, AP 0.5550
Epoch 94, Loss: 0.6706, AUC: 0.5466, AP: 0.5550, Training Time(s): 0.0464, Inference Time(s): 0.0210, Propagation Time(s): 1.3676
Best Epoch 95, AUC 0.5466, AP 0.5551
Epoch 95, Loss: 0.6702, AUC: 0.5466, AP: 0.5551, Training Time(s): 0.0465, Inference Time(s): 0.0228, Propagation Time(s): 1.3676
Best Epoch 96, AUC 0.5467, AP 0.5551
Epoch 96, Loss: 0.6698, AUC: 0.5467, AP: 0.5551, Training Time(s): 0.0455, Inference Time(s): 0.0223, Propagation Time(s): 1.3676
Best Epoch 97, AUC 0.5467, AP 0.5552
Epoch 97, Loss: 0.6696, AUC: 0.5467, AP: 0.5552, Training Time(s): 0.0471, Inference Time(s): 0.0242, Propagation Time(s): 1.3676
Best Epoch 98, AUC 0.5468, AP 0.5553
Epoch 98, Loss: 0.6690, AUC: 0.5468, AP: 0.5553, Training Time(s): 0.0460, Inference Time(s): 0.0198, Propagation Time(s): 1.3676
Best Epoch 99, AUC 0.5469, AP 0.5553
Epoch 99, Loss: 0.6688, AUC: 0.5469, AP: 0.5553, Training Time(s): 0.0454, Inference Time(s): 0.0237, Propagation Time(s): 1.3676
Best Epoch 100, AUC 0.5469, AP 0.5554
Epoch 100, Loss: 0.6683, AUC: 0.5469, AP: 0.5554, Training Time(s): 0.0465, Inference Time(s): 0.0214, Propagation Time(s): 1.3676
Best Epoch 101, AUC 0.5470, AP 0.5555
Epoch 101, Loss: 0.6680, AUC: 0.5470, AP: 0.5555, Training Time(s): 0.0465, Inference Time(s): 0.0240, Propagation Time(s): 1.3676
Best Epoch 102, AUC 0.5470, AP 0.5555
Epoch 102, Loss: 0.6676, AUC: 0.5470, AP: 0.5555, Training Time(s): 0.0463, Inference Time(s): 0.0207, Propagation Time(s): 1.3676
Best Epoch 103, AUC 0.5471, AP 0.5556
Epoch 103, Loss: 0.6672, AUC: 0.5471, AP: 0.5556, Training Time(s): 0.0463, Inference Time(s): 0.0232, Propagation Time(s): 1.3676
Best Epoch 104, AUC 0.5471, AP 0.5555
Epoch 104, Loss: 0.6669, AUC: 0.5471, AP: 0.5555, Training Time(s): 0.0452, Inference Time(s): 0.0206, Propagation Time(s): 1.3676
Epoch 105, Loss: 0.6663, AUC: 0.5469, AP: 0.5548, Training Time(s): 0.0466, Inference Time(s): 0.0237, Propagation Time(s): 1.3676
Epoch 106, Loss: 0.6659, AUC: 0.5469, AP: 0.5548, Training Time(s): 0.0458, Inference Time(s): 0.0204, Propagation Time(s): 1.3676
Epoch 107, Loss: 0.6655, AUC: 0.5469, AP: 0.5549, Training Time(s): 0.0506, Inference Time(s): 0.0209, Propagation Time(s): 1.3676
Epoch 108, Loss: 0.6652, AUC: 0.5470, AP: 0.5550, Training Time(s): 0.0510, Inference Time(s): 0.0182, Propagation Time(s): 1.3676
Epoch 109, Loss: 0.6647, AUC: 0.5471, AP: 0.5551, Training Time(s): 0.0465, Inference Time(s): 0.0241, Propagation Time(s): 1.3676
Best Epoch 110, AUC 0.5472, AP 0.5552
Epoch 110, Loss: 0.6644, AUC: 0.5472, AP: 0.5552, Training Time(s): 0.0457, Inference Time(s): 0.0204, Propagation Time(s): 1.3676
Best Epoch 111, AUC 0.5472, AP 0.5552
Epoch 111, Loss: 0.6639, AUC: 0.5472, AP: 0.5552, Training Time(s): 0.0496, Inference Time(s): 0.0182, Propagation Time(s): 1.3676
Best Epoch 112, AUC 0.5473, AP 0.5554
Epoch 112, Loss: 0.6635, AUC: 0.5473, AP: 0.5554, Training Time(s): 0.0488, Inference Time(s): 0.0224, Propagation Time(s): 1.3676
Best Epoch 113, AUC 0.5474, AP 0.5555
Epoch 113, Loss: 0.6630, AUC: 0.5474, AP: 0.5555, Training Time(s): 0.0458, Inference Time(s): 0.0196, Propagation Time(s): 1.3676
Best Epoch 114, AUC 0.5475, AP 0.5556
Epoch 114, Loss: 0.6627, AUC: 0.5475, AP: 0.5556, Training Time(s): 0.0484, Inference Time(s): 0.0222, Propagation Time(s): 1.3676
Best Epoch 115, AUC 0.5475, AP 0.5557
Epoch 115, Loss: 0.6621, AUC: 0.5475, AP: 0.5557, Training Time(s): 0.0466, Inference Time(s): 0.0253, Propagation Time(s): 1.3676
Best Epoch 116, AUC 0.5476, AP 0.5558
Epoch 116, Loss: 0.6618, AUC: 0.5476, AP: 0.5558, Training Time(s): 0.0457, Inference Time(s): 0.0201, Propagation Time(s): 1.3676
Best Epoch 117, AUC 0.5477, AP 0.5559
Epoch 117, Loss: 0.6612, AUC: 0.5477, AP: 0.5559, Training Time(s): 0.0463, Inference Time(s): 0.0197, Propagation Time(s): 1.3676
Best Epoch 118, AUC 0.5478, AP 0.5560
Epoch 118, Loss: 0.6608, AUC: 0.5478, AP: 0.5560, Training Time(s): 0.0538, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Best Epoch 119, AUC 0.5478, AP 0.5561
Epoch 119, Loss: 0.6602, AUC: 0.5478, AP: 0.5561, Training Time(s): 0.0497, Inference Time(s): 0.0183, Propagation Time(s): 1.3676
Best Epoch 120, AUC 0.5479, AP 0.5561
Epoch 120, Loss: 0.6598, AUC: 0.5479, AP: 0.5561, Training Time(s): 0.0481, Inference Time(s): 0.0205, Propagation Time(s): 1.3676
Best Epoch 121, AUC 0.5479, AP 0.5562
Epoch 121, Loss: 0.6594, AUC: 0.5479, AP: 0.5562, Training Time(s): 0.0455, Inference Time(s): 0.0209, Propagation Time(s): 1.3676
Best Epoch 122, AUC 0.5480, AP 0.5563
Epoch 122, Loss: 0.6590, AUC: 0.5480, AP: 0.5563, Training Time(s): 0.0541, Inference Time(s): 0.0185, Propagation Time(s): 1.3676
Best Epoch 123, AUC 0.5480, AP 0.5564
Epoch 123, Loss: 0.6584, AUC: 0.5480, AP: 0.5564, Training Time(s): 0.0460, Inference Time(s): 0.0200, Propagation Time(s): 1.3676
Best Epoch 124, AUC 0.5481, AP 0.5565
Epoch 124, Loss: 0.6580, AUC: 0.5481, AP: 0.5565, Training Time(s): 0.0672, Inference Time(s): 0.0196, Propagation Time(s): 1.3676
Best Epoch 125, AUC 0.5482, AP 0.5565
Epoch 125, Loss: 0.6576, AUC: 0.5482, AP: 0.5565, Training Time(s): 0.0590, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Best Epoch 126, AUC 0.5482, AP 0.5566
Epoch 126, Loss: 0.6571, AUC: 0.5482, AP: 0.5566, Training Time(s): 0.0530, Inference Time(s): 0.0141, Propagation Time(s): 1.3676
Best Epoch 127, AUC 0.5483, AP 0.5567
Epoch 127, Loss: 0.6566, AUC: 0.5483, AP: 0.5567, Training Time(s): 0.0539, Inference Time(s): 0.0144, Propagation Time(s): 1.3676
Best Epoch 128, AUC 0.5483, AP 0.5568
Epoch 128, Loss: 0.6561, AUC: 0.5483, AP: 0.5568, Training Time(s): 0.0492, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 129, AUC 0.5484, AP 0.5569
Epoch 129, Loss: 0.6557, AUC: 0.5484, AP: 0.5569, Training Time(s): 0.0491, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 130, AUC 0.5484, AP 0.5569
Epoch 130, Loss: 0.6552, AUC: 0.5484, AP: 0.5569, Training Time(s): 0.0490, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 131, AUC 0.5485, AP 0.5570
Epoch 131, Loss: 0.6548, AUC: 0.5485, AP: 0.5570, Training Time(s): 0.0471, Inference Time(s): 0.0181, Propagation Time(s): 1.3676
Best Epoch 132, AUC 0.5485, AP 0.5570
Epoch 132, Loss: 0.6543, AUC: 0.5485, AP: 0.5570, Training Time(s): 0.0443, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 133, AUC 0.5486, AP 0.5571
Epoch 133, Loss: 0.6539, AUC: 0.5486, AP: 0.5571, Training Time(s): 0.0440, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 134, AUC 0.5486, AP 0.5572
Epoch 134, Loss: 0.6534, AUC: 0.5486, AP: 0.5572, Training Time(s): 0.0441, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 135, AUC 0.5487, AP 0.5573
Epoch 135, Loss: 0.6530, AUC: 0.5487, AP: 0.5573, Training Time(s): 0.0456, Inference Time(s): 0.0144, Propagation Time(s): 1.3676
Best Epoch 136, AUC 0.5487, AP 0.5574
Epoch 136, Loss: 0.6526, AUC: 0.5487, AP: 0.5574, Training Time(s): 0.0446, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 137, AUC 0.5488, AP 0.5575
Epoch 137, Loss: 0.6520, AUC: 0.5488, AP: 0.5575, Training Time(s): 0.0443, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 138, AUC 0.5488, AP 0.5575
Epoch 138, Loss: 0.6516, AUC: 0.5488, AP: 0.5575, Training Time(s): 0.0442, Inference Time(s): 0.0145, Propagation Time(s): 1.3676
Best Epoch 139, AUC 0.5489, AP 0.5576
Epoch 139, Loss: 0.6510, AUC: 0.5489, AP: 0.5576, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 140, AUC 0.5489, AP 0.5577
Epoch 140, Loss: 0.6509, AUC: 0.5489, AP: 0.5577, Training Time(s): 0.0438, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 141, AUC 0.5490, AP 0.5577
Epoch 141, Loss: 0.6503, AUC: 0.5490, AP: 0.5577, Training Time(s): 0.0440, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 142, AUC 0.5490, AP 0.5578
Epoch 142, Loss: 0.6499, AUC: 0.5490, AP: 0.5578, Training Time(s): 0.0514, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 143, AUC 0.5491, AP 0.5579
Epoch 143, Loss: 0.6494, AUC: 0.5491, AP: 0.5579, Training Time(s): 0.0463, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 144, AUC 0.5491, AP 0.5579
Epoch 144, Loss: 0.6490, AUC: 0.5491, AP: 0.5579, Training Time(s): 0.0439, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 145, AUC 0.5492, AP 0.5580
Epoch 145, Loss: 0.6487, AUC: 0.5492, AP: 0.5580, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 146, AUC 0.5492, AP 0.5580
Epoch 146, Loss: 0.6481, AUC: 0.5492, AP: 0.5580, Training Time(s): 0.0439, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 147, AUC 0.5493, AP 0.5581
Epoch 147, Loss: 0.6476, AUC: 0.5493, AP: 0.5581, Training Time(s): 0.0461, Inference Time(s): 0.0168, Propagation Time(s): 1.3676
Best Epoch 148, AUC 0.5494, AP 0.5582
Epoch 148, Loss: 0.6472, AUC: 0.5494, AP: 0.5582, Training Time(s): 0.0441, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 149, AUC 0.5494, AP 0.5583
Epoch 149, Loss: 0.6468, AUC: 0.5494, AP: 0.5583, Training Time(s): 0.0442, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 150, AUC 0.5495, AP 0.5584
Epoch 150, Loss: 0.6463, AUC: 0.5495, AP: 0.5584, Training Time(s): 0.0443, Inference Time(s): 0.0182, Propagation Time(s): 1.3676
Best Epoch 151, AUC 0.5496, AP 0.5584
Epoch 151, Loss: 0.6460, AUC: 0.5496, AP: 0.5584, Training Time(s): 0.0439, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 152, AUC 0.5496, AP 0.5584
Epoch 152, Loss: 0.6453, AUC: 0.5496, AP: 0.5584, Training Time(s): 0.0439, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 153, AUC 0.5497, AP 0.5585
Epoch 153, Loss: 0.6450, AUC: 0.5497, AP: 0.5585, Training Time(s): 0.0439, Inference Time(s): 0.0139, Propagation Time(s): 1.3676
Best Epoch 154, AUC 0.5498, AP 0.5586
Epoch 154, Loss: 0.6445, AUC: 0.5498, AP: 0.5586, Training Time(s): 0.0444, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 155, AUC 0.5498, AP 0.5587
Epoch 155, Loss: 0.6441, AUC: 0.5498, AP: 0.5587, Training Time(s): 0.0442, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 156, AUC 0.5499, AP 0.5587
Epoch 156, Loss: 0.6437, AUC: 0.5499, AP: 0.5587, Training Time(s): 0.0443, Inference Time(s): 0.0174, Propagation Time(s): 1.3676
Best Epoch 157, AUC 0.5500, AP 0.5588
Epoch 157, Loss: 0.6435, AUC: 0.5500, AP: 0.5588, Training Time(s): 0.0442, Inference Time(s): 0.0141, Propagation Time(s): 1.3676
Best Epoch 158, AUC 0.5501, AP 0.5588
Epoch 158, Loss: 0.6430, AUC: 0.5501, AP: 0.5588, Training Time(s): 0.0443, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 159, AUC 0.5501, AP 0.5589
Epoch 159, Loss: 0.6426, AUC: 0.5501, AP: 0.5589, Training Time(s): 0.0511, Inference Time(s): 0.0176, Propagation Time(s): 1.3676
Best Epoch 160, AUC 0.5502, AP 0.5589
Epoch 160, Loss: 0.6421, AUC: 0.5502, AP: 0.5589, Training Time(s): 0.0444, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 161, AUC 0.5503, AP 0.5590
Epoch 161, Loss: 0.6419, AUC: 0.5503, AP: 0.5590, Training Time(s): 0.0442, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 162, AUC 0.5503, AP 0.5591
Epoch 162, Loss: 0.6414, AUC: 0.5503, AP: 0.5591, Training Time(s): 0.0438, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 163, AUC 0.5504, AP 0.5591
Epoch 163, Loss: 0.6409, AUC: 0.5504, AP: 0.5591, Training Time(s): 0.0444, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 164, AUC 0.5505, AP 0.5591
Epoch 164, Loss: 0.6405, AUC: 0.5505, AP: 0.5591, Training Time(s): 0.0443, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 165, AUC 0.5505, AP 0.5592
Epoch 165, Loss: 0.6403, AUC: 0.5505, AP: 0.5592, Training Time(s): 0.0441, Inference Time(s): 0.0139, Propagation Time(s): 1.3676
Best Epoch 166, AUC 0.5506, AP 0.5593
Epoch 166, Loss: 0.6398, AUC: 0.5506, AP: 0.5593, Training Time(s): 0.0441, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 167, AUC 0.5507, AP 0.5594
Epoch 167, Loss: 0.6393, AUC: 0.5507, AP: 0.5594, Training Time(s): 0.0513, Inference Time(s): 0.0141, Propagation Time(s): 1.3676
Best Epoch 168, AUC 0.5508, AP 0.5594
Epoch 168, Loss: 0.6390, AUC: 0.5508, AP: 0.5594, Training Time(s): 0.0442, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 169, AUC 0.5509, AP 0.5595
Epoch 169, Loss: 0.6389, AUC: 0.5509, AP: 0.5595, Training Time(s): 0.0442, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 170, AUC 0.5510, AP 0.5596
Epoch 170, Loss: 0.6383, AUC: 0.5510, AP: 0.5596, Training Time(s): 0.0482, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 171, AUC 0.5511, AP 0.5596
Epoch 171, Loss: 0.6379, AUC: 0.5511, AP: 0.5596, Training Time(s): 0.0471, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 172, AUC 0.5511, AP 0.5597
Epoch 172, Loss: 0.6375, AUC: 0.5511, AP: 0.5597, Training Time(s): 0.0466, Inference Time(s): 0.0141, Propagation Time(s): 1.3676
Best Epoch 173, AUC 0.5512, AP 0.5598
Epoch 173, Loss: 0.6373, AUC: 0.5512, AP: 0.5598, Training Time(s): 0.0440, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 174, AUC 0.5513, AP 0.5599
Epoch 174, Loss: 0.6368, AUC: 0.5513, AP: 0.5599, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 175, AUC 0.5514, AP 0.5599
Epoch 175, Loss: 0.6366, AUC: 0.5514, AP: 0.5599, Training Time(s): 0.0448, Inference Time(s): 0.0184, Propagation Time(s): 1.3676
Best Epoch 176, AUC 0.5515, AP 0.5600
Epoch 176, Loss: 0.6362, AUC: 0.5515, AP: 0.5600, Training Time(s): 0.0483, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 177, AUC 0.5515, AP 0.5600
Epoch 177, Loss: 0.6360, AUC: 0.5515, AP: 0.5600, Training Time(s): 0.0439, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 178, AUC 0.5516, AP 0.5601
Epoch 178, Loss: 0.6355, AUC: 0.5516, AP: 0.5601, Training Time(s): 0.0444, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 179, AUC 0.5517, AP 0.5601
Epoch 179, Loss: 0.6351, AUC: 0.5517, AP: 0.5601, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 180, AUC 0.5518, AP 0.5602
Epoch 180, Loss: 0.6348, AUC: 0.5518, AP: 0.5602, Training Time(s): 0.0439, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 181, AUC 0.5519, AP 0.5602
Epoch 181, Loss: 0.6345, AUC: 0.5519, AP: 0.5602, Training Time(s): 0.0443, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 182, AUC 0.5520, AP 0.5603
Epoch 182, Loss: 0.6342, AUC: 0.5520, AP: 0.5603, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 183, AUC 0.5521, AP 0.5604
Epoch 183, Loss: 0.6339, AUC: 0.5521, AP: 0.5604, Training Time(s): 0.0441, Inference Time(s): 0.0148, Propagation Time(s): 1.3676
Best Epoch 184, AUC 0.5522, AP 0.5605
Epoch 184, Loss: 0.6335, AUC: 0.5522, AP: 0.5605, Training Time(s): 0.0442, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 185, AUC 0.5523, AP 0.5605
Epoch 185, Loss: 0.6332, AUC: 0.5523, AP: 0.5605, Training Time(s): 0.0439, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 186, AUC 0.5524, AP 0.5606
Epoch 186, Loss: 0.6330, AUC: 0.5524, AP: 0.5606, Training Time(s): 0.0442, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 187, AUC 0.5524, AP 0.5607
Epoch 187, Loss: 0.6326, AUC: 0.5524, AP: 0.5607, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 188, AUC 0.5525, AP 0.5607
Epoch 188, Loss: 0.6322, AUC: 0.5525, AP: 0.5607, Training Time(s): 0.0441, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 189, AUC 0.5526, AP 0.5608
Epoch 189, Loss: 0.6319, AUC: 0.5526, AP: 0.5608, Training Time(s): 0.0441, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 190, AUC 0.5527, AP 0.5608
Epoch 190, Loss: 0.6317, AUC: 0.5527, AP: 0.5608, Training Time(s): 0.0439, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 191, AUC 0.5528, AP 0.5609
Epoch 191, Loss: 0.6314, AUC: 0.5528, AP: 0.5609, Training Time(s): 0.0438, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 192, AUC 0.5529, AP 0.5610
Epoch 192, Loss: 0.6311, AUC: 0.5529, AP: 0.5610, Training Time(s): 0.0498, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 193, AUC 0.5530, AP 0.5610
Epoch 193, Loss: 0.6311, AUC: 0.5530, AP: 0.5610, Training Time(s): 0.0439, Inference Time(s): 0.0139, Propagation Time(s): 1.3676
Best Epoch 194, AUC 0.5531, AP 0.5611
Epoch 194, Loss: 0.6304, AUC: 0.5531, AP: 0.5611, Training Time(s): 0.0442, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 195, AUC 0.5531, AP 0.5611
Epoch 195, Loss: 0.6303, AUC: 0.5531, AP: 0.5611, Training Time(s): 0.0442, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 196, AUC 0.5532, AP 0.5612
Epoch 196, Loss: 0.6297, AUC: 0.5532, AP: 0.5612, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 197, AUC 0.5533, AP 0.5613
Epoch 197, Loss: 0.6296, AUC: 0.5533, AP: 0.5613, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 198, AUC 0.5534, AP 0.5614
Epoch 198, Loss: 0.6295, AUC: 0.5534, AP: 0.5614, Training Time(s): 0.0438, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 199, AUC 0.5535, AP 0.5614
Epoch 199, Loss: 0.6292, AUC: 0.5535, AP: 0.5614, Training Time(s): 0.0439, Inference Time(s): 0.0144, Propagation Time(s): 1.3676
Best Epoch 200, AUC 0.5536, AP 0.5615
Epoch 200, Loss: 0.6287, AUC: 0.5536, AP: 0.5615, Training Time(s): 0.0439, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 201, AUC 0.5537, AP 0.5615
Epoch 201, Loss: 0.6284, AUC: 0.5537, AP: 0.5615, Training Time(s): 0.0444, Inference Time(s): 0.0145, Propagation Time(s): 1.3676
Best Epoch 202, AUC 0.5538, AP 0.5616
Epoch 202, Loss: 0.6284, AUC: 0.5538, AP: 0.5616, Training Time(s): 0.0456, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 203, AUC 0.5539, AP 0.5617
Epoch 203, Loss: 0.6279, AUC: 0.5539, AP: 0.5617, Training Time(s): 0.0446, Inference Time(s): 0.0141, Propagation Time(s): 1.3676
Best Epoch 204, AUC 0.5539, AP 0.5617
Epoch 204, Loss: 0.6278, AUC: 0.5539, AP: 0.5617, Training Time(s): 0.0440, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 205, AUC 0.5540, AP 0.5618
Epoch 205, Loss: 0.6276, AUC: 0.5540, AP: 0.5618, Training Time(s): 0.0442, Inference Time(s): 0.0141, Propagation Time(s): 1.3676
Best Epoch 206, AUC 0.5541, AP 0.5619
Epoch 206, Loss: 0.6273, AUC: 0.5541, AP: 0.5619, Training Time(s): 0.0442, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 207, AUC 0.5542, AP 0.5619
Epoch 207, Loss: 0.6268, AUC: 0.5542, AP: 0.5619, Training Time(s): 0.0477, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 208, AUC 0.5543, AP 0.5620
Epoch 208, Loss: 0.6269, AUC: 0.5543, AP: 0.5620, Training Time(s): 0.0429, Inference Time(s): 0.0144, Propagation Time(s): 1.3676
Best Epoch 209, AUC 0.5544, AP 0.5621
Epoch 209, Loss: 0.6268, AUC: 0.5544, AP: 0.5621, Training Time(s): 0.0422, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 210, AUC 0.5546, AP 0.5622
Epoch 210, Loss: 0.6263, AUC: 0.5546, AP: 0.5622, Training Time(s): 0.0421, Inference Time(s): 0.0144, Propagation Time(s): 1.3676
Best Epoch 211, AUC 0.5547, AP 0.5622
Epoch 211, Loss: 0.6262, AUC: 0.5547, AP: 0.5622, Training Time(s): 0.0428, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 212, AUC 0.5547, AP 0.5623
Epoch 212, Loss: 0.6257, AUC: 0.5547, AP: 0.5623, Training Time(s): 0.0426, Inference Time(s): 0.0169, Propagation Time(s): 1.3676
Best Epoch 213, AUC 0.5548, AP 0.5623
Epoch 213, Loss: 0.6253, AUC: 0.5548, AP: 0.5623, Training Time(s): 0.0420, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 214, AUC 0.5549, AP 0.5624
Epoch 214, Loss: 0.6252, AUC: 0.5549, AP: 0.5624, Training Time(s): 0.0395, Inference Time(s): 0.0182, Propagation Time(s): 1.3676
Best Epoch 215, AUC 0.5549, AP 0.5624
Epoch 215, Loss: 0.6253, AUC: 0.5549, AP: 0.5624, Training Time(s): 0.0394, Inference Time(s): 0.0174, Propagation Time(s): 1.3676
Best Epoch 216, AUC 0.5550, AP 0.5625
Epoch 216, Loss: 0.6247, AUC: 0.5550, AP: 0.5625, Training Time(s): 0.0389, Inference Time(s): 0.0165, Propagation Time(s): 1.3676
Best Epoch 217, AUC 0.5552, AP 0.5626
Epoch 217, Loss: 0.6246, AUC: 0.5552, AP: 0.5626, Training Time(s): 0.0390, Inference Time(s): 0.0142, Propagation Time(s): 1.3676
Best Epoch 218, AUC 0.5553, AP 0.5626
Epoch 218, Loss: 0.6243, AUC: 0.5553, AP: 0.5626, Training Time(s): 0.0444, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 219, AUC 0.5553, AP 0.5627
Epoch 219, Loss: 0.6238, AUC: 0.5553, AP: 0.5627, Training Time(s): 0.0390, Inference Time(s): 0.0166, Propagation Time(s): 1.3676
Best Epoch 220, AUC 0.5554, AP 0.5627
Epoch 220, Loss: 0.6239, AUC: 0.5554, AP: 0.5627, Training Time(s): 0.0394, Inference Time(s): 0.0168, Propagation Time(s): 1.3676
Best Epoch 221, AUC 0.5555, AP 0.5628
Epoch 221, Loss: 0.6237, AUC: 0.5555, AP: 0.5628, Training Time(s): 0.0391, Inference Time(s): 0.0167, Propagation Time(s): 1.3676
Best Epoch 222, AUC 0.5556, AP 0.5629
Epoch 222, Loss: 0.6236, AUC: 0.5556, AP: 0.5629, Training Time(s): 0.0391, Inference Time(s): 0.0178, Propagation Time(s): 1.3676
Best Epoch 223, AUC 0.5557, AP 0.5630
Epoch 223, Loss: 0.6234, AUC: 0.5557, AP: 0.5630, Training Time(s): 0.0386, Inference Time(s): 0.0183, Propagation Time(s): 1.3676
Best Epoch 224, AUC 0.5558, AP 0.5631
Epoch 224, Loss: 0.6226, AUC: 0.5558, AP: 0.5631, Training Time(s): 0.0389, Inference Time(s): 0.0182, Propagation Time(s): 1.3676
Best Epoch 225, AUC 0.5559, AP 0.5631
Epoch 225, Loss: 0.6228, AUC: 0.5559, AP: 0.5631, Training Time(s): 0.0387, Inference Time(s): 0.0172, Propagation Time(s): 1.3676
Best Epoch 226, AUC 0.5560, AP 0.5632
Epoch 226, Loss: 0.6227, AUC: 0.5560, AP: 0.5632, Training Time(s): 0.0391, Inference Time(s): 0.0169, Propagation Time(s): 1.3676
Best Epoch 227, AUC 0.5561, AP 0.5632
Epoch 227, Loss: 0.6224, AUC: 0.5561, AP: 0.5632, Training Time(s): 0.0437, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 228, AUC 0.5562, AP 0.5633
Epoch 228, Loss: 0.6221, AUC: 0.5562, AP: 0.5633, Training Time(s): 0.0387, Inference Time(s): 0.0169, Propagation Time(s): 1.3676
Best Epoch 229, AUC 0.5562, AP 0.5633
Epoch 229, Loss: 0.6221, AUC: 0.5562, AP: 0.5633, Training Time(s): 0.0392, Inference Time(s): 0.0169, Propagation Time(s): 1.3676
Best Epoch 230, AUC 0.5564, AP 0.5634
Epoch 230, Loss: 0.6219, AUC: 0.5564, AP: 0.5634, Training Time(s): 0.0388, Inference Time(s): 0.0182, Propagation Time(s): 1.3676
Best Epoch 231, AUC 0.5565, AP 0.5635
Epoch 231, Loss: 0.6214, AUC: 0.5565, AP: 0.5635, Training Time(s): 0.0429, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 232, AUC 0.5566, AP 0.5636
Epoch 232, Loss: 0.6214, AUC: 0.5566, AP: 0.5636, Training Time(s): 0.0386, Inference Time(s): 0.0179, Propagation Time(s): 1.3676
Best Epoch 233, AUC 0.5567, AP 0.5636
Epoch 233, Loss: 0.6211, AUC: 0.5567, AP: 0.5636, Training Time(s): 0.0391, Inference Time(s): 0.0166, Propagation Time(s): 1.3676
Best Epoch 234, AUC 0.5567, AP 0.5637
Epoch 234, Loss: 0.6211, AUC: 0.5567, AP: 0.5637, Training Time(s): 0.0387, Inference Time(s): 0.0178, Propagation Time(s): 1.3676
Best Epoch 235, AUC 0.5569, AP 0.5638
Epoch 235, Loss: 0.6208, AUC: 0.5569, AP: 0.5638, Training Time(s): 0.0391, Inference Time(s): 0.0171, Propagation Time(s): 1.3676
Best Epoch 236, AUC 0.5570, AP 0.5639
Epoch 236, Loss: 0.6206, AUC: 0.5570, AP: 0.5639, Training Time(s): 0.0390, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Best Epoch 237, AUC 0.5571, AP 0.5639
Epoch 237, Loss: 0.6205, AUC: 0.5571, AP: 0.5639, Training Time(s): 0.0386, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Best Epoch 238, AUC 0.5572, AP 0.5640
Epoch 238, Loss: 0.6201, AUC: 0.5572, AP: 0.5640, Training Time(s): 0.0409, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 239, AUC 0.5574, AP 0.5641
Epoch 239, Loss: 0.6200, AUC: 0.5574, AP: 0.5641, Training Time(s): 0.0389, Inference Time(s): 0.0168, Propagation Time(s): 1.3676
Best Epoch 240, AUC 0.5575, AP 0.5642
Epoch 240, Loss: 0.6197, AUC: 0.5575, AP: 0.5642, Training Time(s): 0.0395, Inference Time(s): 0.0185, Propagation Time(s): 1.3676
Best Epoch 241, AUC 0.5576, AP 0.5642
Epoch 241, Loss: 0.6197, AUC: 0.5576, AP: 0.5642, Training Time(s): 0.0390, Inference Time(s): 0.0178, Propagation Time(s): 1.3676
Best Epoch 242, AUC 0.5576, AP 0.5643
Epoch 242, Loss: 0.6196, AUC: 0.5576, AP: 0.5643, Training Time(s): 0.0391, Inference Time(s): 0.0168, Propagation Time(s): 1.3676
Best Epoch 243, AUC 0.5577, AP 0.5644
Epoch 243, Loss: 0.6191, AUC: 0.5577, AP: 0.5644, Training Time(s): 0.0390, Inference Time(s): 0.0179, Propagation Time(s): 1.3676
Best Epoch 244, AUC 0.5578, AP 0.5644
Epoch 244, Loss: 0.6190, AUC: 0.5578, AP: 0.5644, Training Time(s): 0.0389, Inference Time(s): 0.0195, Propagation Time(s): 1.3676
Best Epoch 245, AUC 0.5580, AP 0.5645
Epoch 245, Loss: 0.6188, AUC: 0.5580, AP: 0.5645, Training Time(s): 0.0389, Inference Time(s): 0.0172, Propagation Time(s): 1.3676
Best Epoch 246, AUC 0.5581, AP 0.5646
Epoch 246, Loss: 0.6187, AUC: 0.5581, AP: 0.5646, Training Time(s): 0.0393, Inference Time(s): 0.0179, Propagation Time(s): 1.3676
Best Epoch 247, AUC 0.5581, AP 0.5647
Epoch 247, Loss: 0.6184, AUC: 0.5581, AP: 0.5647, Training Time(s): 0.0389, Inference Time(s): 0.0204, Propagation Time(s): 1.3676
Best Epoch 248, AUC 0.5582, AP 0.5647
Epoch 248, Loss: 0.6183, AUC: 0.5582, AP: 0.5647, Training Time(s): 0.0387, Inference Time(s): 0.0178, Propagation Time(s): 1.3676
Best Epoch 249, AUC 0.5584, AP 0.5648
Epoch 249, Loss: 0.6181, AUC: 0.5584, AP: 0.5648, Training Time(s): 0.0387, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Best Epoch 250, AUC 0.5585, AP 0.5649
Epoch 250, Loss: 0.6183, AUC: 0.5585, AP: 0.5649, Training Time(s): 0.0392, Inference Time(s): 0.0175, Propagation Time(s): 1.3676
Best Epoch 251, AUC 0.5585, AP 0.5649
Epoch 251, Loss: 0.6177, AUC: 0.5585, AP: 0.5649, Training Time(s): 0.0388, Inference Time(s): 0.0165, Propagation Time(s): 1.3676
Best Epoch 252, AUC 0.5587, AP 0.5650
Epoch 252, Loss: 0.6176, AUC: 0.5587, AP: 0.5650, Training Time(s): 0.0390, Inference Time(s): 0.0202, Propagation Time(s): 1.3676
Best Epoch 253, AUC 0.5588, AP 0.5651
Epoch 253, Loss: 0.6174, AUC: 0.5588, AP: 0.5651, Training Time(s): 0.0396, Inference Time(s): 0.0202, Propagation Time(s): 1.3676
Best Epoch 254, AUC 0.5589, AP 0.5651
Epoch 254, Loss: 0.6174, AUC: 0.5589, AP: 0.5651, Training Time(s): 0.0400, Inference Time(s): 0.0173, Propagation Time(s): 1.3676
Best Epoch 255, AUC 0.5589, AP 0.5652
Epoch 255, Loss: 0.6168, AUC: 0.5589, AP: 0.5652, Training Time(s): 0.0395, Inference Time(s): 0.0174, Propagation Time(s): 1.3676
Best Epoch 256, AUC 0.5591, AP 0.5653
Epoch 256, Loss: 0.6167, AUC: 0.5591, AP: 0.5653, Training Time(s): 0.0393, Inference Time(s): 0.0167, Propagation Time(s): 1.3676
Best Epoch 257, AUC 0.5592, AP 0.5654
Epoch 257, Loss: 0.6171, AUC: 0.5592, AP: 0.5654, Training Time(s): 0.0399, Inference Time(s): 0.0166, Propagation Time(s): 1.3676
Best Epoch 258, AUC 0.5593, AP 0.5655
Epoch 258, Loss: 0.6166, AUC: 0.5593, AP: 0.5655, Training Time(s): 0.0418, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 259, AUC 0.5594, AP 0.5655
Epoch 259, Loss: 0.6165, AUC: 0.5594, AP: 0.5655, Training Time(s): 0.0388, Inference Time(s): 0.0170, Propagation Time(s): 1.3676
Best Epoch 260, AUC 0.5595, AP 0.5656
Epoch 260, Loss: 0.6164, AUC: 0.5595, AP: 0.5656, Training Time(s): 0.0386, Inference Time(s): 0.0176, Propagation Time(s): 1.3676
Best Epoch 261, AUC 0.5596, AP 0.5657
Epoch 261, Loss: 0.6161, AUC: 0.5596, AP: 0.5657, Training Time(s): 0.0456, Inference Time(s): 0.0140, Propagation Time(s): 1.3676
Best Epoch 262, AUC 0.5596, AP 0.5657
Epoch 262, Loss: 0.6161, AUC: 0.5596, AP: 0.5657, Training Time(s): 0.0386, Inference Time(s): 0.0163, Propagation Time(s): 1.3676
Best Epoch 263, AUC 0.5598, AP 0.5658
Epoch 263, Loss: 0.6159, AUC: 0.5598, AP: 0.5658, Training Time(s): 0.0389, Inference Time(s): 0.0177, Propagation Time(s): 1.3676
Best Epoch 264, AUC 0.5599, AP 0.5659
Epoch 264, Loss: 0.6157, AUC: 0.5599, AP: 0.5659, Training Time(s): 0.0424, Inference Time(s): 0.0143, Propagation Time(s): 1.3676
Best Epoch 265, AUC 0.5599, AP 0.5659
Epoch 265, Loss: 0.6155, AUC: 0.5599, AP: 0.5659, Training Time(s): 0.0386, Inference Time(s): 0.0171, Propagation Time(s): 1.3676
Best Epoch 266, AUC 0.5601, AP 0.5660
Epoch 266, Loss: 0.6155, AUC: 0.5601, AP: 0.5660, Training Time(s): 0.0386, Inference Time(s): 0.0163, Propagation Time(s): 1.3676
Best Epoch 267, AUC 0.5601, AP 0.5661
Epoch 267, Loss: 0.6151, AUC: 0.5601, AP: 0.5661, Training Time(s): 0.0385, Inference Time(s): 0.0177, Propagation Time(s): 1.3676
Best Epoch 268, AUC 0.5602, AP 0.5661
Epoch 268, Loss: 0.6152, AUC: 0.5602, AP: 0.5661, Training Time(s): 0.0392, Inference Time(s): 0.0171, Propagation Time(s): 1.3676
Best Epoch 269, AUC 0.5604, AP 0.5662
Epoch 269, Loss: 0.6151, AUC: 0.5604, AP: 0.5662, Training Time(s): 0.0416, Inference Time(s): 0.0169, Propagation Time(s): 1.3676
Best Epoch 270, AUC 0.5605, AP 0.5663
Epoch 270, Loss: 0.6149, AUC: 0.5605, AP: 0.5663, Training Time(s): 0.0390, Inference Time(s): 0.0167, Propagation Time(s): 1.3676
Best Epoch 271, AUC 0.5605, AP 0.5663
Epoch 271, Loss: 0.6147, AUC: 0.5605, AP: 0.5663, Training Time(s): 0.0388, Inference Time(s): 0.0174, Propagation Time(s): 1.3676
Best Epoch 272, AUC 0.5606, AP 0.5664
Epoch 272, Loss: 0.6143, AUC: 0.5606, AP: 0.5664, Training Time(s): 0.0387, Inference Time(s): 0.0228, Propagation Time(s): 1.3676
Best Epoch 273, AUC 0.5608, AP 0.5665
Epoch 273, Loss: 0.6145, AUC: 0.5608, AP: 0.5665, Training Time(s): 0.0392, Inference Time(s): 0.0177, Propagation Time(s): 1.3676
Best Epoch 274, AUC 0.5608, AP 0.5666
Epoch 274, Loss: 0.6141, AUC: 0.5608, AP: 0.5666, Training Time(s): 0.0387, Inference Time(s): 0.0179, Propagation Time(s): 1.3676
Best Epoch 275, AUC 0.5609, AP 0.5667
Epoch 275, Loss: 0.6141, AUC: 0.5609, AP: 0.5667, Training Time(s): 0.0449, Inference Time(s): 0.0141, Propagation Time(s): 1.3676
Best Epoch 276, AUC 0.5611, AP 0.5668
Epoch 276, Loss: 0.6138, AUC: 0.5611, AP: 0.5668, Training Time(s): 0.0385, Inference Time(s): 0.0173, Propagation Time(s): 1.3676
Best Epoch 277, AUC 0.5611, AP 0.5668
Epoch 277, Loss: 0.6137, AUC: 0.5611, AP: 0.5668, Training Time(s): 0.0387, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Best Epoch 278, AUC 0.5611, AP 0.5668
Epoch 278, Loss: 0.6136, AUC: 0.5611, AP: 0.5668, Training Time(s): 0.0390, Inference Time(s): 0.0203, Propagation Time(s): 1.3676
Best Epoch 279, AUC 0.5613, AP 0.5669
Epoch 279, Loss: 0.6137, AUC: 0.5613, AP: 0.5669, Training Time(s): 0.0431, Inference Time(s): 0.0182, Propagation Time(s): 1.3676
Best Epoch 280, AUC 0.5614, AP 0.5670
Epoch 280, Loss: 0.6135, AUC: 0.5614, AP: 0.5670, Training Time(s): 0.0387, Inference Time(s): 0.0172, Propagation Time(s): 1.3676
Best Epoch 281, AUC 0.5614, AP 0.5670
Epoch 281, Loss: 0.6130, AUC: 0.5614, AP: 0.5670, Training Time(s): 0.0386, Inference Time(s): 0.0166, Propagation Time(s): 1.3676
Best Epoch 282, AUC 0.5616, AP 0.5671
Epoch 282, Loss: 0.6131, AUC: 0.5616, AP: 0.5671, Training Time(s): 0.0393, Inference Time(s): 0.0169, Propagation Time(s): 1.3676
Best Epoch 283, AUC 0.5617, AP 0.5672
Epoch 283, Loss: 0.6132, AUC: 0.5617, AP: 0.5672, Training Time(s): 0.0391, Inference Time(s): 0.0206, Propagation Time(s): 1.3676
Best Epoch 284, AUC 0.5617, AP 0.5672
Epoch 284, Loss: 0.6131, AUC: 0.5617, AP: 0.5672, Training Time(s): 0.0397, Inference Time(s): 0.0176, Propagation Time(s): 1.3676
Best Epoch 285, AUC 0.5618, AP 0.5673
Epoch 285, Loss: 0.6129, AUC: 0.5618, AP: 0.5673, Training Time(s): 0.0406, Inference Time(s): 0.0174, Propagation Time(s): 1.3676
Best Epoch 286, AUC 0.5619, AP 0.5674
Epoch 286, Loss: 0.6123, AUC: 0.5619, AP: 0.5674, Training Time(s): 0.0436, Inference Time(s): 0.0189, Propagation Time(s): 1.3676
Best Epoch 287, AUC 0.5620, AP 0.5674
Epoch 287, Loss: 0.6128, AUC: 0.5620, AP: 0.5674, Training Time(s): 0.0409, Inference Time(s): 0.0202, Propagation Time(s): 1.3676
Best Epoch 288, AUC 0.5620, AP 0.5674
Epoch 288, Loss: 0.6122, AUC: 0.5620, AP: 0.5674, Training Time(s): 0.0461, Inference Time(s): 0.0159, Propagation Time(s): 1.3676
Best Epoch 289, AUC 0.5621, AP 0.5675
Epoch 289, Loss: 0.6121, AUC: 0.5621, AP: 0.5675, Training Time(s): 0.0432, Inference Time(s): 0.0225, Propagation Time(s): 1.3676
Best Epoch 290, AUC 0.5622, AP 0.5676
Epoch 290, Loss: 0.6122, AUC: 0.5622, AP: 0.5676, Training Time(s): 0.0398, Inference Time(s): 0.0177, Propagation Time(s): 1.3676
Best Epoch 291, AUC 0.5622, AP 0.5676
Epoch 291, Loss: 0.6121, AUC: 0.5622, AP: 0.5676, Training Time(s): 0.0437, Inference Time(s): 0.0145, Propagation Time(s): 1.3676
Best Epoch 292, AUC 0.5623, AP 0.5677
Epoch 292, Loss: 0.6121, AUC: 0.5623, AP: 0.5677, Training Time(s): 0.0467, Inference Time(s): 0.0148, Propagation Time(s): 1.3676
Best Epoch 293, AUC 0.5625, AP 0.5678
Epoch 293, Loss: 0.6117, AUC: 0.5625, AP: 0.5678, Training Time(s): 0.0423, Inference Time(s): 0.0205, Propagation Time(s): 1.3676
Best Epoch 294, AUC 0.5625, AP 0.5678
Epoch 294, Loss: 0.6115, AUC: 0.5625, AP: 0.5678, Training Time(s): 0.0390, Inference Time(s): 0.0197, Propagation Time(s): 1.3676
Best Epoch 295, AUC 0.5625, AP 0.5678
Epoch 295, Loss: 0.6114, AUC: 0.5625, AP: 0.5678, Training Time(s): 0.0431, Inference Time(s): 0.0146, Propagation Time(s): 1.3676
Best Epoch 296, AUC 0.5627, AP 0.5679
Epoch 296, Loss: 0.6116, AUC: 0.5627, AP: 0.5679, Training Time(s): 0.0391, Inference Time(s): 0.0168, Propagation Time(s): 1.3676
Best Epoch 297, AUC 0.5628, AP 0.5680
Epoch 297, Loss: 0.6112, AUC: 0.5628, AP: 0.5680, Training Time(s): 0.0408, Inference Time(s): 0.0185, Propagation Time(s): 1.3676
Best Epoch 298, AUC 0.5628, AP 0.5680
Epoch 298, Loss: 0.6111, AUC: 0.5628, AP: 0.5680, Training Time(s): 0.0400, Inference Time(s): 0.0205, Propagation Time(s): 1.3676
Best Epoch 299, AUC 0.5629, AP 0.5681
Epoch 299, Loss: 0.6110, AUC: 0.5629, AP: 0.5681, Training Time(s): 0.0474, Inference Time(s): 0.0180, Propagation Time(s): 1.3676
Best Epoch 300, AUC 0.5630, AP 0.5681
Epoch 300, Loss: 0.6109, AUC: 0.5630, AP: 0.5681, Training Time(s): 0.0488, Inference Time(s): 0.0220, Propagation Time(s): 1.3676
Best Epoch 300, AUC 0.5630, AP 0.5681
Total time: 128.8210s